# LocalDateTime序列化问题解决方案（完整版）

## 问题描述

在任务动态记录功能中出现了以下错误：

```
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.type.TypeException: Could not set parameters for mapping: ParameterMapping{property='contentJson', mode=IN, javaType=interface java.util.Map, jdbcType=null, numericScale=null, resultMapId='null', jdbcTypeName='null', expression='null'}. Cause: org.apache.ibatis.type.TypeException: Error setting non null for parameter #4 with JdbcType null . Try setting a different JdbcType for this parameter or a different configuration property. Cause: java.lang.RuntimeException: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Java 8 date/time type `java.time.LocalDateTime` not supported by default: add Module "com.fasterxml.jackson.datatype:jackson-datatype-jsr310" to enable handling (through reference chain: java.util.HashMap["dueDate"])
```

## 问题根因分析

1. **问题出现位置**：`TaskActivityService.recordCreateActivity()` 方法在记录任务创建动态时
2. **直接原因**：`JacksonTypeHandler` 在序列化包含 `LocalDateTime` 类型的 Map 对象时失败
3. **根本原因**：
   - MyBatis-Plus的默认 `JacksonTypeHandler` 使用的 `ObjectMapper` 实例没有注册 JSR310 模块
   - 项目缺少 `jackson-datatype-jsr310` 依赖
   - 自定义的 TypeHandler 中使用的 `ObjectMapper` 实例没有配置 JSR310 模块

## 完整解决方案

### 1. 添加 JSR310 依赖

在 `pom.xml` 中添加：

```xml
<!-- Jackson JSR310模块支持Java 8时间类型 -->
<dependency>
    <groupId>com.fasterxml.jackson.datatype</groupId>
    <artifactId>jackson-datatype-jsr310</artifactId>
</dependency>
```

### 2. 创建统一的 Jackson 配置类

创建 `JacksonConfig.java` 配置类：

```java
@Configuration
public class JacksonConfig {
    
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 注册 JSR310 模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        
        // 配置时间类型的序列化和反序列化格式
        javaTimeModule.addSerializer(LocalDateTime.class, 
            new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDateTime.class, 
            new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        objectMapper.registerModule(javaTimeModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        return objectMapper;
    }
    
    // 提供静态方法供 TypeHandler 使用
    public static ObjectMapper getSharedObjectMapper() {
        // ... 同样的配置逻辑
    }
}
```

### 3. 创建自定义 TypeHandler 替换 MyBatis-Plus 默认处理器

由于 MyBatis-Plus 的默认 `JacksonTypeHandler` 使用内置的 ObjectMapper，我们需要创建自定义的 TypeHandler：

#### CustomMapTypeHandler（处理 Map<String, Object>）
```java
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(Map.class)
public class CustomMapTypeHandler extends BaseTypeHandler<Map<String, Object>> {
    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();
    
    // 实现序列化和反序列化逻辑
}
```

#### CustomListStringTypeHandler（处理 List<String>）
```java
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class CustomListStringTypeHandler extends BaseTypeHandler<List<String>> {
    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();
    
    // 实现序列化和反序列化逻辑
}
```

### 4. 更新 PO 类使用自定义 TypeHandler

#### TaskActivityPO
```java
// 原来
@TableField(value = "content_json", typeHandler = JacksonTypeHandler.class)
private Map<String, Object> contentJson;

// 修改为
@TableField(value = "content_json", typeHandler = cn.july.orch.meeting.config.CustomMapTypeHandler.class)
private Map<String, Object> contentJson;
```

#### TaskPO
```java
// 修改 attachmentsJson 字段
@TableField(value = "attachments_json", typeHandler = cn.july.orch.meeting.config.CustomListStringTypeHandler.class)
private List<String> attachmentsJson;
```

#### NewMeetingPO 和 MeetingPlanPO
```java
// 修改 preMeetingDocuments 和 actualAttendees 字段
@TableField(value = "pre_meeting_documents", typeHandler = cn.july.orch.meeting.config.CustomListStringTypeHandler.class)
private List<String> preMeetingDocuments;

@TableField(value = "actual_attendees", typeHandler = cn.july.orch.meeting.config.CustomListStringTypeHandler.class)
private List<String> actualAttendees;
```

修改以下 TypeHandler 类，使用配置好的 ObjectMapper：

#### AgendaPlanTypeHandler
```java
// 原来
private static final ObjectMapper objectMapper = new ObjectMapper();

// 修改为
private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();
```

#### CheckInConfigTypeHandler
```java
// 原来 
private static final ObjectMapper objectMapper = new ObjectMapper();

// 修改为
private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();
```

#### FilePartDetailService 和 FileDetailService
```java
// 原来
private final ObjectMapper objectMapper = new ObjectMapper();

// 修改为
private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();
```

### 3. 添加 Spring Boot Jackson 配置

在 `application.yml` 中添加：

```yaml
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
```

## 技术实现要点

### 1. JSR310 模块注册
- 使用 `JavaTimeModule` 处理 Java 8 时间类型
- 配置统一的日期时间格式：`yyyy-MM-dd HH:mm:ss`
- 禁用时间戳序列化，使用可读的字符串格式

### 2. 共享 ObjectMapper 实例
- 通过静态方法 `getSharedObjectMapper()` 提供配置一致的实例
- 避免在各个 TypeHandler 中重复创建和配置
- 确保所有地方使用相同的序列化配置

### 3. 配置优先级
- `@Primary` 确保 Spring 优先使用我们配置的 ObjectMapper Bean
- Web 层和数据层使用相同的 Jackson 配置
- 保证整个应用的时间类型处理一致性

## 测试验证

### 验证步骤
1. **重启应用**：确保新的配置生效
2. **创建任务**：验证任务创建功能正常
3. **检查日志**：确认任务动态记录成功，无错误日志
4. **数据库验证**：检查 `task_activities` 表中是否正确记录了动态信息

### 预期结果
- ✅ 任务创建成功，返回任务 ID
- ✅ 任务动态记录成功，包含 `LocalDateTime` 字段
- ✅ `content_json` 字段正确序列化时间类型
- ✅ 日志中不再出现 Jackson 序列化错误

## 涉及的文件

### 新增文件
- `JacksonConfig.java` - Jackson 配置类
- `CustomMapTypeHandler.java` - 自定义 Map 类型处理器
- `CustomListStringTypeHandler.java` - 自定义 List<String> 类型处理器

### 修改文件
- `pom.xml` - 添加 jackson-datatype-jsr310 依赖
- `application.yml` - Spring Boot Jackson 配置
- `AgendaPlanTypeHandler.java` - 议程计划类型处理器
- `CheckInConfigTypeHandler.java` - 签到配置类型处理器  
- `FilePartDetailService.java` - 文件分片服务
- `FileDetailService.java` - 文件详情服务
- `TaskActivityPO.java` - 任务动态数据对象
- `TaskPO.java` - 任务数据对象
- `NewMeetingPO.java` - 新会议数据对象
- `MeetingPlanPO.java` - 会议计划数据对象

## 最佳实践

### 1. 统一配置管理
- 所有 JSON 序列化配置集中在 `JacksonConfig` 类中
- 避免在各个组件中重复配置 ObjectMapper
- 提供静态方法供非 Spring 管理的类使用

### 2. 时间类型处理规范
- 统一使用 `yyyy-MM-dd HH:mm:ss` 格式
- 设置正确的时区 `GMT+8`
- 禁用时间戳序列化，提高可读性

### 3. 异常处理
- 任务动态记录失败不影响主业务流程
- 详细的错误日志便于问题排查
- 优雅降级，保证核心功能可用

## 总结

通过创建统一的 Jackson 配置类并更新所有相关的 TypeHandler，成功解决了 `LocalDateTime` 序列化问题。这个解决方案不仅修复了当前的错误，还为未来的 Java 8 时间类型处理提供了标准化的配置，提高了代码的维护性和一致性。