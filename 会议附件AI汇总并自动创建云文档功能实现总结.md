# 会议附件AI汇总并自动创建云文档功能实现总结

## 功能概述

本功能实现了会议附件的AI汇总提炼，并自动创建飞书云文档，为参会人员提供便捷的文档访问体验。

## 实现架构

### 1. 服务层划分

- **july-feishu**: 提供飞书云文档相关的原子能力
- **july-orch-meeting-service**: 业务逻辑层，集成AI汇总和云文档创建

### 2. 核心功能模块

- 飞书云文档服务（上传、导入、权限管理）
- AI文档汇总服务增强（支持多附件合并）
- 会议服务集成（创建/更新会议时触发AI汇总）

## 详细实现

### 1. 飞书云文档原子能力 (july-feishu)

#### DriveService.java
- 上传素材文件到飞书云盘
- 创建导入任务
- 查询导入任务结果
- 批量添加协作者权限

#### 相关模型类
- `UploadMediaRequest`: 上传素材请求模型
- `CreateImportTaskRequest`: 创建导入任务请求模型
- `BatchCreatePermissionMemberRequest`: 批量创建权限成员请求模型
- `ImportTaskResult`: 导入任务结果模型（包含文档token和文件链接）

#### 错误码扩展
在 `FeishuErrorCode.java` 中添加了云盘相关的错误码：
- `DRIVE_UPLOAD_FAIL`: 上传素材文件失败
- `DRIVE_IMPORT_FAIL`: 创建导入任务失败
- `DRIVE_IMPORT_QUERY_FAIL`: 查询导入任务失败
- `DRIVE_PERMISSION_FAIL`: 设置文档权限失败

### 2. AI文档汇总服务增强

#### AiDocumentSummaryService.java 整合功能
- **原有功能**：
  - `summarizeDocuments()`: 对会前文档进行AI汇总
  - `generateMarkdownSummary()`: 生成Markdown格式的汇总内容
  - `convertToMarkdown()`: 将文档汇总转换为Markdown格式
  - 支持多附件合并到一个文档中

- **新增云文档功能**：
  - `createMeetingDocumentSummaryAsync()`: 异步创建会议文档汇总
  - `createMeetingDocumentSummary()`: 同步创建会议文档汇总
  - `processDocumentSummaryAsync()`: 异步处理文档AI汇总并更新会议描述
  - `uploadMarkdownFile()`: 上传Markdown文件到飞书云盘
  - `createImportTask()`: 创建导入任务
  - `waitForImportTaskCompletion()`: 等待导入任务完成
  - `setDocumentPermissions()`: 设置文档权限
  - `cleanupTempFile()`: 清理单个临时文件
  - `cleanupTempDirectory()`: 清理临时目录中的过期文件

- **完整的文档创建流程**：
  1. 生成Markdown汇总内容
  2. 在项目根目录的 `temp` 文件夹中创建临时文件
  3. 上传文件到飞书云盘
  4. 创建导入任务
  5. 查询导入任务结果（返回文档token和文件链接）
  6. 设置文档权限
  7. 使用导入任务返回的文档链接
  8. 清理临时文件

### 3. 会议服务集成

#### NewMeetingActionService.java 修改
- 在 `processDocumentSummaryAsync()` 方法中委托给 `AiDocumentSummaryService` 处理
- 在更新会议时检测附件变更并触发AI汇总
- 为 `NewMeetingUpdateCommand` 添加 `enableDocAiSummary` 字段
- 简化了服务调用，将具体的文档汇总逻辑完全委托给 `AiDocumentSummaryService`

### 4. 临时文件管理

#### TempFileCleanupConfig.java
- 定时任务配置，每小时清理一次临时文件
- 自动清理超过1小时的临时文件
- 确保项目根目录的 `temp` 文件夹不会积累过多临时文件
- 调用 `AiDocumentSummaryService.cleanupTempDirectory()` 方法

## 功能流程

### 创建会议时的处理流程
1. 用户创建会议并上传附件
2. 如果启用AI汇总，异步处理文档汇总
3. 调用AI服务对每个附件进行汇总
4. 生成Markdown格式的汇总内容
5. 创建飞书云文档
6. 设置参会人员权限
7. 更新会议描述，添加云文档链接

### 更新会议时的处理流程
1. 用户更新会议附件
2. 检测附件是否发生变化
3. 如果附件变更且启用AI汇总，重新处理
4. 重复创建会议时的处理流程

## 技术特点

### 1. 异步处理
- 使用 `@Async` 注解实现异步处理，不阻塞主流程
- 使用 `CompletableFuture` 处理异步结果

### 2. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 优雅的降级处理

### 3. 权限管理
- 自动为参会人员设置文档访问权限
- 支持批量权限设置

### 4. 文件处理
- 支持多种文件格式
- 自动计算文件校验和
- 临时文件管理：
  - 在项目根目录的 `temp` 文件夹中创建临时文件
  - 处理完成后立即删除临时文件
  - 定时清理过期的临时文件（超过1小时）

## 配置要求

### 1. 飞书应用配置
- 需要云盘相关权限
- 需要文档导入权限
- 需要权限管理权限

### 2. 依赖配置
- 确保 `july-feishu` 模块已正确配置
- 确保飞书SDK版本支持相关API

## 使用说明

### 1. 创建会议
```java
NewMeetingCreateCommand command = NewMeetingCreateCommand.builder()
    .meetingName("项目评审会议")
    .preMeetingDocumentKeys(Arrays.asList("file1", "file2"))
    .enableDocAiSummary(true)  // 启用AI汇总
    .build();
```

### 2. 更新会议
```java
NewMeetingUpdateCommand command = NewMeetingUpdateCommand.builder()
    .id(meetingId)
    .preMeetingDocuments(Arrays.asList("file3", "file4"))
    .enableDocAiSummary(true)  // 启用AI汇总
    .build();
```

## 注意事项

1. **文件大小限制**: 飞书云盘对单个文件大小有限制，超过20MB需要使用分片上传
2. **权限设置**: 确保飞书应用有足够的权限进行文档操作
3. **异步处理**: 文档创建是异步的，可能需要一定时间完成
4. **错误处理**: 如果AI汇总失败，会记录错误但不影响会议创建
5. **临时文件**: 系统会在项目根目录的 `temp` 文件夹中创建临时文件，处理完成后立即删除，并定时清理过期文件

## 技术改进

### 导入任务结果优化
- **问题**: 原来需要手动构造文档链接，可能存在格式不一致的问题
- **解决方案**: 直接使用飞书导入任务返回的 `url` 字段作为文档链接
- **改进效果**: 
  - 确保文档链接格式正确
  - 减少手动构造链接的错误
  - 提高代码的可维护性

### 返回结果结构优化
- **新增**: `ImportTaskResult` 类，包含文档token和文件链接两个字段
- **优势**: 一次调用获取完整信息，避免多次API调用

### 服务职责整合
- **问题**: 原来AI汇总和云文档创建功能分散在两个服务中，职责不够清晰
- **解决方案**: 将云文档创建功能整合到 `AiDocumentSummaryService` 中
- **改进效果**:
  - 统一AI文档汇总相关的所有功能
  - 简化服务调用关系
  - 提高代码的内聚性和可维护性
  - 删除冗余的 `FeishuCloudDocumentService` 类

### 异步处理优化
- **问题**: 云文档创建过程耗时较长，同步调用会影响用户体验
- **解决方案**: 使用 `createMeetingDocumentSummaryAsync()` 异步方法
- **改进效果**:
  - 使用 `CompletableFuture` 进行异步处理
  - 通过 `thenAccept()` 处理成功结果
  - 通过 `exceptionally()` 处理异常情况
  - 提高系统响应速度和用户体验

### 服务职责进一步整合
- **问题**: `processDocumentSummaryAsync` 方法在 `NewMeetingActionService` 中，但实际是文档汇总的业务逻辑
- **解决方案**: 将 `processDocumentSummaryAsync` 方法移动到 `AiDocumentSummaryService` 中
- **改进效果**:
  - 完全统一了AI文档汇总相关的所有功能
  - `NewMeetingActionService` 现在只需要简单委托调用
  - 进一步提高了代码的内聚性和可维护性
  - 服务职责更加清晰明确

### 用户Token异步处理优化
- **问题**: 飞书API需要用户token调用，但 `@Async` 注解的方法会在新线程中执行，导致用户上下文丢失
- **解决方案**: 
  - 在 `NewMeetingActionService` 的 `@Async` 方法中获取用户token：`FeishuAppContext.get().getUserAccessToken()`
  - 将用户token作为参数传递给 `AiDocumentSummaryService.processDocumentSummaryAsync()`
  - 移除 `AiDocumentSummaryService.processDocumentSummaryAsync()` 的 `@Async` 注解，改为普通方法
  - 在 `DriveService` 中添加支持用户token的重载方法
- **改进效果**:
  - 确保异步处理中能够正确调用飞书API
  - 避免了 `@Async` 导致的用户上下文丢失问题
  - 保持了异步处理的性能优势
  - 所有飞书API调用都使用正确的用户身份
  - 解决了Spring异步方法无法获取ThreadLocal上下文的问题

## 扩展建议

1. **支持更多文件格式**: 可以扩展支持更多文档格式的AI汇总
2. **自定义模板**: 可以支持用户自定义Markdown模板
3. **批量处理**: 可以支持批量会议的文档汇总
4. **缓存机制**: 可以添加缓存机制避免重复处理相同文档
5. **监控告警**: 可以添加监控和告警机制，及时发现处理异常