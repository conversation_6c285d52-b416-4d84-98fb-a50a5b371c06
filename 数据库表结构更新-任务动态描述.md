# 任务动态表结构更新脚本

## 为task_activities表添加activity_description字段

```sql
-- 添加动态描述字段
ALTER TABLE task_activities 
ADD COLUMN activity_description VARCHAR(255) NOT NULL COMMENT '动态描述 (一句话简单描述)' 
AFTER activity_type;

-- 为已有数据填充默认描述（如果有数据的话）
UPDATE task_activities 
SET activity_description = CASE 
    WHEN activity_type = 'CREATE' THEN '创建了任务'
    WHEN activity_type = 'UPDATE_STATUS' THEN '更新了任务状态'
    WHEN activity_type = 'UPDATE_INFO' THEN '更新了任务信息'
    WHEN activity_type = 'ADD_COMMENT' THEN '添加了评论'
    WHEN activity_type = 'ADD_ATTACHMENT' THEN '添加了附件'
    WHEN activity_type = 'REMOVE_ATTACHMENT' THEN '移除了附件'
    WHEN activity_type = 'ASSIGN_USER' THEN '分配了任务'
    WHEN activity_type = 'SET_DUE_DATE' THEN '设置了截止时间'
    WHEN activity_type = 'COMPLETE' THEN '完成了任务'
    WHEN activity_type = 'START' THEN '开始处理任务'
    WHEN activity_type = 'DECOMPOSE' THEN '拆解了任务'
    WHEN activity_type = 'DELETE' THEN '删除了任务'
    WHEN activity_type = 'SUPERVISE' THEN '督办了任务'
    WHEN activity_type = 'REMIND' THEN '提醒了任务'
    WHEN activity_type = 'ESCALATE' THEN '升级督办了任务'
    WHEN activity_type = 'OVERDUE' THEN '任务已超期'
    WHEN activity_type = 'POSTPONE' THEN '延期了任务'
    ELSE '执行了操作'
END
WHERE activity_description IS NULL OR activity_description = '';
```

## 完整的task_activities表结构

```sql
CREATE TABLE task_activities (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_id BIGINT UNSIGNED NOT NULL COMMENT '关联的任务ID (逻辑关联 tasks.id)',
    activity_type VARCHAR(50) NOT NULL COMMENT '动态类型',
    activity_description VARCHAR(255) NOT NULL COMMENT '动态描述 (一句话简单描述)',
    content_json JSON NULL COMMENT '动态内容JSON (存储结构化数据)',
    deleted TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '逻辑删除 (0: 未删除, 1: 已删除)',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_id VARCHAR(50) NOT NULL COMMENT '创建用户ID',
    create_user_name VARCHAR(100) NOT NULL COMMENT '创建用户名',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_user_id VARCHAR(50) NOT NULL COMMENT '更新用户ID',
    update_user_name VARCHAR(100) NOT NULL COMMENT '更新用户名',
    INDEX idx_task_id (task_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_create_time (create_time)
) COMMENT='任务动态表';
```

## 页面展示格式

动态信息页面展示格式为：**时间 + 用户名 + 动态描述**

示例：
- `2025-08-26 10:30` `张三` `创建了任务「系统优化」`
- `2025-08-26 11:15` `李四` `将任务状态从「未开始」更新为「进行中」`  
- `2025-08-26 14:20` `王五` `对任务进行一般督办`
- `2025-08-26 16:30` `赵六` `完成了任务`