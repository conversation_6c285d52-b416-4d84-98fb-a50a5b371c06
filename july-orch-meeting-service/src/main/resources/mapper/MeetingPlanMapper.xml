<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingPlanMapper">

    <select id="queryAllHistoricalPlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            planned_start_time >= #{query.startDate} AND planned_start_time &lt; query.endDate}
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagId !=null">
                and tag_id LIKE CONCAT('%', #{query.tagId}, '%')
            </if>
        </where>
    </select>

    <!-- 查询未来的一次性会议规划 -->
    <select id="queryFutureOneTimePlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            planned_start_time >= #{query.startDate} AND planned_start_time &lt; query.endDate}
            and (cron is null or cron = '')
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagId !=null">
                and tag_id LIKE CONCAT('%', #{query.tagId}, '%')
            </if>
        </where>

    </select>

    <!-- 查询未来的重复性会议规划 -->
    <select id="queryFutureRecurringPlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            and cron is not null and cron != ''
            and status = 0
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagId !=null">
                and tag_id LIKE CONCAT('%', #{query.tagId}, '%')
            </if>
        </where>
    </select>

</mapper>
