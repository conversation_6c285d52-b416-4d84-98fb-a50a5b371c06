-- 会议室信息表 (与飞书同步)
CREATE TABLE meeting_room
(
    id                 bigint auto_increment comment '主键ID'
        primary key,
    fs_room_id         varchar(128)         not null comment '飞书会议室ID (room_id)',
    name               varchar(100)         not null comment '会议室名称',
    capacity           int unsigned         not null comment '会议室能容纳的人数',
    description        text                 null comment '会议室的相关描述',
    devices            json                 null comment '设施信息列表 (同步自飞书的device结构)',
    -- 该字段表示会议室自身的基础状态，由管理员维护
    status             tinyint    default 0 not null comment '状态 (0-空闲中, 1-使用中, 2-已预定)',

    create_time        datetime             not null comment '创建时间',
    update_time        datetime             not null comment '更新时间',
    deleted            tinyint    default 0 not null comment '删除标记 (0-未删除, 1-已删除)',
    -- 唯一索引，确保飞书会议室ID不重复
    UNIQUE KEY uk_fs_room_id (fs_room_id)
) comment '会议室信息表 (与飞书同步)';