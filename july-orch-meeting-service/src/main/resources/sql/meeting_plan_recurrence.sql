-- 为 meeting_plan 表添加重复会议相关字段
ALTER TABLE `meeting_plan`
ADD COLUMN `is_recurring` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否重复会议',
ADD COLUMN `recurrence_type` varchar(20) COMMENT '重复类型(DAILY/WEEKLY/MONTHLY)',
ADD COLUMN `recurrence_interval` int(11) DEFAULT 1 COMMENT '重复间隔(每N天/周/月)',
ADD COLUMN `recurrence_weekdays` varchar(50) COMMENT '每周重复的星期几(1,2,3,4,5,6,7)',
ADD COLUMN `recurrence_month_day` int(11) COMMENT '每月重复的日期(1-31)',
ADD COLUMN `recurrence_end_date` date COMMENT '重复结束日期';

-- 添加索引
ALTER TABLE `meeting_plan`
ADD KEY `idx_is_recurring` (`is_recurring`),
ADD KEY `idx_recurrence_end_date` (`recurrence_end_date`);

