-- 会议标签表
CREATE TABLE meeting_tags
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    name             varchar(50)       not null comment '标签名称',
    color            varchar(7)        not null comment '标签颜色 (Hex格式, e.g., #3498DB)',
    description      varchar(255)      null comment '标签描述',
    
    -- 标准审计字段
    create_user_id   varchar(50)       not null comment '创建人ID',
    create_user_name varchar(100)      null comment '创建人姓名',
    create_time      datetime          not null comment '创建时间',
    update_user_id   varchar(50)       null comment '更新人ID',
    update_user_name varchar(100)      null comment '更新人姓名',
    update_time      datetime          not null comment '更新时间',
    deleted          tinyint default 0 not null comment '删除标记(0-未删除,1-已删除)',
    
    constraint uk_tag_name
        unique (name, deleted)
)
    comment '会议标签表';

-- 会议标准与会议标签的关联表
CREATE TABLE meeting_standard_tag_pivot
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    standard_id      bigint            not null comment '会议标准ID, 逻辑关联 meeting_standard.id',
    tag_id           bigint            not null comment '会议标签ID, 逻辑关联 meeting_tags.id',
    
    -- 标准审计字段
    create_user_id   varchar(50)       not null comment '创建人ID',
    create_user_name varchar(100)      null comment '创建人姓名',
    create_time      datetime          not null comment '创建时间',
    update_user_id   varchar(50)       null comment '更新人ID',
    update_user_name varchar(100)      null comment '更新人姓名',
    update_time      datetime          not null comment '更新时间',
    deleted          tinyint default 0 not null comment '删除标记(0-未删除,1-已删除)',
    
    constraint uk_standard_tag
        unique (standard_id, tag_id)
)
    comment '会议标准与会议标签的关联表';

-- 修改会议标准表，新增agenda_plan和check_in_config两个JSON字段
ALTER TABLE meeting_standard 
ADD COLUMN agenda_plan json NULL COMMENT '议程规划, 存储为JSON对象数组' AFTER meeting_points,
ADD COLUMN check_in_config json NULL COMMENT '签到配置, 存储为JSON对象' AFTER agenda_plan;