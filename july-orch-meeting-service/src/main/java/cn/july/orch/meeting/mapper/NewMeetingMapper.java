package cn.july.orch.meeting.mapper;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.domain.command.NewMeetingQuery;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议映射器
 * @date 2025-01-24
 */
public interface NewMeetingMapper extends BaseMapper<NewMeetingPO> {

    /**
     * 分页查询新会议列表
     */
    Page<NewMeetingListDTO> queryPage(Page<NewMeetingPO> page, @Param("query") NewMeetingQuery query);

    default NewMeetingPO findByFsCalendarEventId(String fsCalendarEventId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class)
                .eq(NewMeetingPO::getFsCalendarEventId, fsCalendarEventId);
        return selectOne(wrapper);
    }


    default NewMeetingPO findByFsMeetingId(String fsMeetingId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class)
                .eq(NewMeetingPO::getFsMeetingId, fsMeetingId);
        return selectOne(wrapper);
    }


    default IPage<NewMeetingPO> findPage(String meetingName, Integer status, Integer priorityLevel,
                                         Long meetingPlanId, Long meetingStandardId,
                                         String startTimeFrom, String startTimeTo,
                                         String createUserId, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = buildQueryWrapper(meetingName, status, priorityLevel,
                meetingPlanId, meetingStandardId, startTimeFrom, startTimeTo, createUserId);
        wrapper.orderByDesc(NewMeetingPO::getCreateTime);

        Page<NewMeetingPO> page = new Page<>(pageNum, pageSize);
        return selectPage(page, wrapper);
    }


    default Long count(String meetingName, Integer status, Integer priorityLevel,
                       Long meetingPlanId, Long meetingStandardId,
                       String startTimeFrom, String startTimeTo, String createUserId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = buildQueryWrapper(meetingName, status, priorityLevel,
                meetingPlanId, meetingStandardId, startTimeFrom, startTimeTo, createUserId);
        return selectCount(wrapper);
    }

    default LambdaQueryWrapper<NewMeetingPO> buildQueryWrapper(String meetingName, Integer status, Integer priorityLevel,
                                                               Long meetingPlanId, Long meetingStandardId,
                                                               String startTimeFrom, String startTimeTo, String createUserId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);

        // 逻辑删除条件：只查询未删除的数据
        wrapper.eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);

        if (StringUtils.hasText(meetingName)) {
            wrapper.like(NewMeetingPO::getMeetingName, meetingName);
        }

        if (status != null) {
            wrapper.eq(NewMeetingPO::getStatus, status);
        }

        if (meetingPlanId != null) {
            wrapper.eq(NewMeetingPO::getMeetingPlanId, meetingPlanId);
        }

        if (meetingStandardId != null) {
            wrapper.eq(NewMeetingPO::getMeetingStandardId, meetingStandardId);
        }

        if (StringUtils.hasText(startTimeFrom)) {
            wrapper.ge(NewMeetingPO::getStartTime, startTimeFrom);
        }

        if (StringUtils.hasText(startTimeTo)) {
            wrapper.le(NewMeetingPO::getStartTime, startTimeTo);
        }

        if (StringUtils.hasText(createUserId)) {
            wrapper.eq(NewMeetingPO::getCreateUserId, createUserId);
        }

        return wrapper;
    }


    default void updateStatus(Long id, NewMeetingStatusEnum status) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setStatus(status);
        updateById(po);
    }


    default void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setFsCalendarEventId(fsCalendarEventId);
        po.setFsMeetingId(fsMeetingId);
        po.setMeetingUrl(meetingUrl);
        updateById(po);
    }


    default void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMeetingNo(meetingNo);
        po.setFsMeetingId(fsMeetingId);
        updateById(po);
    }


    default void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMeetingNo(meetingNo);
        po.setFsMeetingId(fsMeetingId);
        updateById(po);
    }


    default void updateMinuteUrl(Long id, String minuteUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMinuteUrl(minuteUrl);
        updateById(po);
    }

    /**
     * 根据会议室ID和时间范围查询会议
     */
    default List<NewMeetingPO> findByMeetingRoomIdAndTimeRange(
            Long meetingRoomId,
            LocalDateTime startTime,
            LocalDateTime endTime,
            int pageNum,
            int pageSize) {

        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);
        wrapper.eq(NewMeetingPO::getMeetingRoomId, meetingRoomId)
                .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED)
                .ge(startTime != null, NewMeetingPO::getStartTime, startTime)
                .lt(endTime != null, NewMeetingPO::getStartTime, endTime)
                .orderByDesc(NewMeetingPO::getStartTime);

        Page<NewMeetingPO> page = new Page<>(pageNum, pageSize);
        return selectPage(page, wrapper).getRecords();
    }

    /**
     * 根据会议室ID和时间范围统计会议数量
     */
    default Long countByMeetingRoomIdAndTimeRange(
            Long meetingRoomId,
            LocalDateTime startTime,
            LocalDateTime endTime) {

        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);
        wrapper.eq(NewMeetingPO::getMeetingRoomId, meetingRoomId)
                .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED)
                .ge(startTime != null, NewMeetingPO::getStartTime, startTime)
                .lt(endTime != null, NewMeetingPO::getStartTime, endTime);

        return selectCount(wrapper);
    }

    /**
     * 根据ID查询会议详情
     */
    default NewMeetingPO getDetailById(Long id) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);
        wrapper.eq(NewMeetingPO::getId, id)
                .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectOne(wrapper);
    }

    default List<NewMeetingPO> selectByPlanIds(List<Long> planIds) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);
        wrapper.in(NewMeetingPO::getMeetingPlanId, planIds);
        return selectList(wrapper);
    }
}