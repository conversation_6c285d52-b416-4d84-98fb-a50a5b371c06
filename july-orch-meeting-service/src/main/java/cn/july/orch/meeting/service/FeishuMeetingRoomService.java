package cn.july.orch.meeting.service;

import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import cn.july.orch.meeting.repository.MeetingRoomRepository;
import com.lark.oapi.service.vc.v1.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 飞书会议室服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuMeetingRoomService {

    private final FeishuAppClient feishuAppClient;
    private final MeetingRoomRepository meetingRoomRepository;

    /**
     * 同步飞书会议室数据
     */
    public void syncMeetingRooms() {
        try {
            log.info("开始同步飞书会议室数据");

            // 分页获取所有会议室
            String pageToken = null;
            Boolean hasMore;
            do {
                // 发起请求
                ListRoomRespBody resp = feishuAppClient.getRoomService().listMeetingRooms(100, pageToken);

                // 处理当前页的会议室数据
                if (resp.getRooms() != null) {
                    processRooms(resp.getRooms());
                }

                // 更新分页标记
                pageToken = resp.getPageToken();
                hasMore = resp.getHasMore();

            } while (hasMore && pageToken != null && !pageToken.isEmpty());

            log.info("飞书会议室数据同步完成");
        } catch (Exception e) {
            log.error("同步飞书会议室数据失败", e);
        }
    }

    /**
     * 处理会议室数据
     *
     * @param rooms 会议室列表
     */
    private void processRooms(Room[] rooms) {
        if (rooms == null || rooms.length == 0) {
            return;
        }

        log.info("处理{}个会议室数据", rooms.length);

        for (Room room : rooms) {
            try {
                // 转换飞书会议室数据为系统会议室实体
                MeetingRoom meetingRoom = convertToMeetingRoom(room);

                // 检查是否已存在
                MeetingRoom existing = meetingRoomRepository.findByFsRoomId(meetingRoom.getFsRoomId());
                if (existing == null) {
                    // 新增会议室
                    meetingRoomRepository.save(meetingRoom);
                    log.info("新增会议室: {}", meetingRoom.getName());
                } else {
                    // 更新现有会议室信息
                    meetingRoom.setId(existing.getId());
                    meetingRoom.setCreateTime(existing.getCreateTime()); // 保持创建时间不变
                    meetingRoomRepository.save(meetingRoom);
                    log.info("更新会议室: {}", meetingRoom.getName());
                }
            } catch (Exception e) {
                log.error("处理会议室数据失败，会议室ID: {}", room.getRoomId(), e);
            }
        }
    }

    /**
     * 转换飞书会议室数据为系统会议室实体
     *
     * @param room 飞书会议室对象
     * @return 系统会议室实体
     */
    private MeetingRoom convertToMeetingRoom(Room room) {
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setFsRoomId(room.getRoomId());
        meetingRoom.setName(room.getName());
        meetingRoom.setCapacity(room.getCapacity() != null ? room.getCapacity().intValue() : 0);
        meetingRoom.setDescription(room.getDescription());
        meetingRoom.setStatus(MeetingRoomStatusEnum.FREE); // 默认空闲状态
        meetingRoom.setCreateTime(LocalDateTime.now());
        meetingRoom.setUpdateTime(LocalDateTime.now());
        meetingRoom.setDeleted(0);

        // 处理设备信息，只获取设备名称
        if (room.getDevice() != null && room.getDevice().length > 0) {
            List<String> deviceNames = new ArrayList<>();
            for (Device device : room.getDevice()) {
                if (device != null && device.getName() != null) {
                    deviceNames.add(device.getName());
                }
            }
            meetingRoom.setDevices(deviceNames);
        }

        return meetingRoom;
    }
}