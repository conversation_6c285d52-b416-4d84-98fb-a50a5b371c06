package cn.july.orch.meeting.processor;

import cn.hutool.core.util.ObjUtil;
import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class MeetingPlanProcessor {

    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingPlanMapper meetingPlanMapper;

    public MeetingStandardPO checkCreatePlan(MeetingPlanCreateCommand command){
        MeetingStandardPO standard = meetingStandardMapper.selectById(command.getMeetingStandardId());
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }
        if (ObjUtil.isNotNull(command.getPlannedStartTime()) && command.getPlannedStartTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("会议开始时间必须在当前时间之后");
        }
        return standard;
    }

    public MeetingStandardPO checkUpdatePlan(MeetingPlanUpdateCommand command){
        MeetingPlanPO oldPlanPO = meetingPlanMapper.selectById(command.getId());
        if (oldPlanPO == null) {
            throw new BusinessException("会议规划不存在");
        }
        if (!oldPlanPO.getStatus().equals(MeetingPlanStatusEnum.NOT_STARTED)) {
            throw new BusinessException("只有未开始会议支持编辑");
        }

        // 2. 验证会议标准
        MeetingStandardPO standard = meetingStandardMapper.selectById(command.getMeetingStandardId());
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }

        // 3. 验证会议开始时间必须在当前时间之后
        if (ObjUtil.isNotNull(command.getPlannedStartTime()) && command.getPlannedStartTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("会议开始时间必须在当前时间之后");
        }
        return standard;
    }
}
