package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.exception.BusinessException;
import cn.july.core.utils.CronUtils;
import cn.july.orch.meeting.assembler.MeetingPlanAssembler;
import cn.july.orch.meeting.assembler.MeetingStandardAssembler;
import cn.july.orch.meeting.assembler.MeetingTagAssembler;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.MeetingPlanDTO;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.po.MeetingTagPO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import cn.july.orch.meeting.mapper.MeetingTagMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.processor.MeetingPlanProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议规划服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingPlanService {

    private final MeetingPlanMapper meetingPlanMapper;
    private final MeetingPlanAssembler meetingPlanAssembler;
    private final UserInfoQueryService userInfoQueryService;
    private final MeetingPlanProcessor meetingPlanProcessor;
    private final MeetingStandardAssembler meetingStandardAssembler;
    private final MeetingTagAssembler meetingTagAssembler;
    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingTagMapper meetingTagMapper;
    private final NewMeetingAssembler newMeetingAssembler;
    private final NewMeetingMapper newMeetingMapper;

    /**
     * 创建会议规划
     */
    @Transactional
    public void createMeetingPlan(MeetingPlanCreateCommand command) {

        MeetingStandardPO standard = meetingPlanProcessor.checkCreatePlan(command);

        MeetingPlanPO meetingPlanPO = meetingPlanAssembler.toEntity(command);
        meetingPlanPO.setStatus(MeetingPlanStatusEnum.NOT_STARTED);
        meetingPlanPO.setAdvanceNoticeSent(0);

        //处理重复规则
        if (StrUtil.isNotBlank(command.getCron())) {
            Integer defaultDuration = standard.getDefaultDuration();
            meetingPlanPO.setPlannedDuration(defaultDuration);
            //对于重复会议来说,开始结束时间表示下次规划的时间.
            LocalDateTime nextTime = CronUtils.nextExecutionTime(command.getCron(), LocalDateTime.now());
            meetingPlanPO.setPlannedStartTime(nextTime);
            meetingPlanPO.setPlannedEndTime(nextTime.plusMinutes(defaultDuration));
            meetingPlanPO.setUuid(IdUtil.fastSimpleUUID());
            if (ObjUtil.isNotNull(command.getRecurrenceEndDate()) && command.getRecurrenceEndDate().isBefore(nextTime)) {
                throw new BusinessException("当前日期到重复会议截止时间中间必须包含一次可执行时间");
            }
        } else {
            long minutes = Duration.between(command.getPlannedStartTime(), command.getPlannedEndTime()).toMinutes();
            meetingPlanPO.setPlannedDuration((int) minutes);
        }
        meetingPlanMapper.insert(meetingPlanPO);
    }

    /**
     * 更新会议规划
     */
    @Transactional
    public void updateMeetingPlan(MeetingPlanUpdateCommand command) {
        MeetingStandardPO standard = meetingPlanProcessor.checkUpdatePlan(command);
        MeetingPlanPO planPO = meetingPlanMapper.selectById(command.getId());

        planPO.setPlanName(command.getPlanName());
        planPO.setPlanDescription(command.getPlanDescription());
        planPO.setMeetingStandardId(command.getMeetingStandardId());
        planPO.setAttendees(command.getAttendees());
        planPO.setPreMeetingDocuments(command.getPreMeetingDocuments());
        planPO.setTagIds(command.getTagIds());
        //重复规则
        if (StrUtil.isNotBlank(command.getCron())) {
            Integer defaultDuration = standard.getDefaultDuration();
            planPO.setPlannedDuration(defaultDuration);
            //对于重复会议来说,开始结束时间表示下次规划的时间.
            LocalDateTime nextTime = CronUtils.nextExecutionTime(command.getCron(), LocalDateTime.now());
            planPO.setPlannedStartTime(nextTime);
            planPO.setPlannedEndTime(nextTime.plusMinutes(defaultDuration));
            if (ObjUtil.isNotNull(command.getRecurrenceEndDate()) && command.getRecurrenceEndDate().isBefore(nextTime)) {
                throw new BusinessException("当前日期到重复会议截止时间中间必须包含一次可执行时间");
            }
        } else {
            long minutes = Duration.between(command.getPlannedStartTime(), command.getPlannedEndTime()).toMinutes();
            planPO.setPlannedDuration((int) minutes);
            planPO.setPlannedStartTime(command.getPlannedStartTime());
            planPO.setPlannedEndTime(command.getPlannedEndTime());
        }

        meetingPlanMapper.updateById(planPO);
    }

    /**
     * 删除会议规划
     */
    @Transactional
    public void deleteMeetingPlan(Long id) {
        MeetingPlanPO meetingPlanPO = meetingPlanMapper.selectById(id);
        if (meetingPlanPO == null) {
            throw new BusinessException("会议规划不存在");
        }
        if (meetingPlanPO.getStatus() != MeetingPlanStatusEnum.NOT_STARTED) {
            throw new BusinessException("只有未开始的会议规划可以删除");
        }
        meetingPlanMapper.deleteById(id);
    }

    /**
     * 根据ID查询会议规划详情
     */
    public MeetingPlanDTO getById(Long id) {
        MeetingPlanPO po = meetingPlanMapper.selectById(id);
        if (po == null) {
            return null;
        }
        MeetingPlanDTO result = arrangeData(Collections.singletonList(meetingPlanAssembler.PO2DTO(po))).get(0);

        //关联已完成会议
        List<Long> planIds = Collections.singletonList(result.getId());
        //重复会议显示所有有关的
        if (StrUtil.isNotBlank(result.getCron())) {
            String uuid = result.getUuid();
            List<MeetingPlanPO> pos = meetingPlanMapper.selectByUuid(uuid);
            planIds = pos.stream().map(MeetingPlanPO::getId).distinct().collect(Collectors.toList());
        }
        List<NewMeetingPO> meetingPOS = newMeetingMapper.selectByPlanIds(planIds);
        if (CollUtil.isNotEmpty(meetingPOS)) {
            result.setNewMeetings(newMeetingAssembler.PO2DTO(meetingPOS));
        }
        return result;
    }

    /**
     * 查询日历维度的会议规划
     * 根据查询时间范围与当前时间的关系，采用不同的查询策略
     */
    public List<MeetingPlanDTO> queryCalendar(MeetingPlanCalendarQuery query) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime queryStart = query.getStartDate();
        LocalDateTime queryEnd = query.getEndDate();

        List<MeetingPlanDTO> allPlans = new ArrayList<>();

        // 1. 判断查询时间范围与当前时间的关系
        if (queryEnd.isBefore(now)) {
            // 情况1：查询范围完全在当前时间之前
            // 无论是否重复性会议，按查询条件一次查出
            allPlans.addAll(queryHistoricalPlans(query));

        } else if (queryStart.isAfter(now)) {
            // 情况2：查询范围完全在当前时间之后
            // 一次性会议查询出来，重复性会议特殊处理
            allPlans.addAll(queryFuturePlans(query));

        } else {
            // 情况3：查询范围跨越当前时间
            // 分别处理过去和未来的部分

            // 3.1 处理当前时间之前的部分
            if (queryStart.isBefore(now)) {
                MeetingPlanCalendarQuery pastQuery = createSubQuery(query, queryStart, now);
                allPlans.addAll(queryHistoricalPlans(pastQuery));
            }

            // 3.2 处理当前时间之后的部分
            if (queryEnd.isAfter(now)) {
                MeetingPlanCalendarQuery futureQuery = createSubQuery(query, now, queryEnd);
                allPlans.addAll(queryFuturePlans(futureQuery));
            }
        }
        //扩展字段
        arrangeData(allPlans);
        // 2. 排序并返回
        return allPlans.stream()
                .sorted(Comparator.comparing(MeetingPlanDTO::getPlannedStartTime))
                .collect(Collectors.toList());
    }

    /**
     * 更新逾期状态
     */
    public void updateOverdueStatus() {
        List<MeetingPlanPO> overduePlans = meetingPlanMapper.findOverduePlans();
        if (CollUtil.isEmpty(overduePlans)) {
            return;
        }
        List<Long> ids = overduePlans.stream().map(MeetingPlanPO::getId).collect(Collectors.toList());
        meetingPlanMapper.updateStatusByIds(ids, MeetingPlanStatusEnum.OVERDUE);
        //重复会议额外处理
        overduePlans.stream().filter(po -> StrUtil.isNotBlank(po.getCron())).forEach(po -> {
            MeetingPlanPO newPO = new MeetingPlanPO();
            BeanUtils.copyProperties(po, newPO);

            LocalDateTime nextTime = CronUtils.nextExecutionTime(po.getCron(), LocalDateTime.now());
            newPO.setPlannedStartTime(nextTime);
            newPO.setPlannedEndTime(nextTime.plusMinutes(po.getPlannedDuration()));
            newPO.setStatus(MeetingPlanStatusEnum.NOT_STARTED);
            newPO.setAdvanceNoticeSent(0);
            newPO.setId(null);
            meetingPlanMapper.insert(newPO);
        });
    }

    /**
     * 查询未完成会议
     *
     * @param query
     * @return
     */
    public List<MeetingPlanDTO> notStartMeeting(MeetingPlanCalendarQuery query) {
        List<MeetingPlanPO> poList = meetingPlanMapper.selectNotStartMeeting(query);
        return arrangeData(meetingPlanAssembler.PO2DTO(poList));
    }

    /**
     * 查询历史会议规划（当前时间之前的部分）
     * 无论是否重复性会议，都按查询条件一次查出
     */
    private List<MeetingPlanDTO> queryHistoricalPlans(MeetingPlanCalendarQuery query) {
        return meetingPlanAssembler.PO2DTO(meetingPlanMapper.queryAllHistoricalPlans(query));
    }

    /**
     * 查询未来会议规划（当前时间之后的部分）
     * 一次性会议查询出来，重复性会议特殊处理
     */
    private List<MeetingPlanDTO> queryFuturePlans(MeetingPlanCalendarQuery query) {
        List<MeetingPlanDTO> result = new ArrayList<>();

        // 1. 查询一次性会议
        List<MeetingPlanPO> oneTimePlanPOs = meetingPlanMapper.queryFutureOneTimePlans(query);
        if (CollUtil.isNotEmpty(oneTimePlanPOs)) {
            result.addAll(meetingPlanAssembler.PO2DTO(oneTimePlanPOs));
        }
        // 2. 查询重复性会议并特殊处理
        List<MeetingPlanPO> recurringPlans = meetingPlanMapper.queryFutureRecurringPlans(query);
        for (MeetingPlanPO po : recurringPlans) {
            //重复实例展开
            List<MeetingPlanPO> futureInstances = expandFutureRecurringInstances(po, query);
            if (CollUtil.isNotEmpty(futureInstances)) {
                result.addAll(meetingPlanAssembler.PO2DTO(futureInstances));
            }

        }
        return result;
    }

    /**
     * 展开历史重复性会议实例
     */
    private List<MeetingPlanPO> expandFutureRecurringInstances(MeetingPlanPO po, MeetingPlanCalendarQuery query) {
        List<MeetingPlanPO> result = new ArrayList<>();
        String cron = po.getCron();

        LocalDateTime nextTime = LocalDateTime.now();
        while (true) {
            nextTime = CronUtils.nextExecutionTime(cron, nextTime);
            if (nextTime.isAfter(query.getEndDate())) {
                break;
            }
            if (nextTime.isAfter(po.getRecurrenceEndDate())) {
                break;
            }
            MeetingPlanPO planPO = new MeetingPlanPO();
            BeanUtils.copyProperties(po, planPO);
            planPO.setPlannedStartTime(nextTime);
            planPO.setPlannedEndTime(nextTime.plusMinutes(po.getPlannedDuration()));
            result.add(planPO);
        }
        return result;
    }

    /**
     * 创建子查询对象
     */
    private MeetingPlanCalendarQuery createSubQuery(MeetingPlanCalendarQuery originalQuery, LocalDateTime startDate, LocalDateTime endDate) {
        MeetingPlanCalendarQuery subQuery = new MeetingPlanCalendarQuery();
        subQuery.setStartDate(startDate);
        subQuery.setEndDate(endDate);
        subQuery.setTagId(originalQuery.getTagId());
        return subQuery;
    }

    private List<MeetingPlanDTO> arrangeData(List<MeetingPlanDTO> meetingPlanDTOS) {
        //会议标准名称
        List<Long> standardIds = meetingPlanDTOS.stream().map(MeetingPlanDTO::getMeetingStandardId).distinct().collect(Collectors.toList());
        List<MeetingStandardPO> standardPOS = meetingStandardMapper.selectBatchIds(standardIds);
        if (CollUtil.isNotEmpty(standardPOS)) {
            Map<Long, MeetingStandardPO> standardMap = standardPOS.stream().collect(Collectors.toMap(MeetingStandardPO::getId, standard -> standard));
            meetingPlanDTOS.forEach(plan -> {
                MeetingStandardPO standardPO = standardMap.get(plan.getMeetingStandardId());
                plan.setMeetingStandard(meetingStandardAssembler.PO2DTO(standardPO));
            });
        }

        //会议标签
        List<Long> tagIds = meetingPlanDTOS.stream()
                .filter(plan -> CollUtil.isNotEmpty(plan.getTagIds()))
                .flatMap(dto -> dto.getTagIds().stream())
                .distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tagIds)) {
            List<MeetingTagPO> tagPOS = meetingTagMapper.selectBatchIds(tagIds);
            Map<Long, MeetingTagPO> tagMap = tagPOS.stream().collect(Collectors.toMap(MeetingTagPO::getId, tag -> tag));
            meetingPlanDTOS.forEach(plan -> {
                if (plan.getTagIds() != null && !plan.getTagIds().isEmpty()) {
                    List<MeetingTagDTO> tags = plan.getTagIds().stream()
                            .map(tag -> meetingTagAssembler.PO2DTO(tagMap.get(tag)))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    plan.setTags(tags);
                }
            });
        }

        //参会人员信息
        List<String> attendeeIds = meetingPlanDTOS.stream()
                .flatMap(dto -> dto.getAttendees().stream())
                .distinct().collect(Collectors.toList());
        // 批量获取用户信息
        Map<String, FSUserInfoDTO> userInfoMap = userInfoQueryService.getUserInfos(attendeeIds);

        // 为每个会议规划填充发起人详细信息
        meetingPlanDTOS.forEach(plan -> {
            if (plan.getAttendees() != null && !plan.getAttendees().isEmpty()) {
                List<FSUserInfoDTO> attendeeDetails = plan.getAttendees().stream()
                        .map(userInfoMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                plan.setAttendeeDetails(attendeeDetails);
            }
        });
        return meetingPlanDTOS;
    }

}
