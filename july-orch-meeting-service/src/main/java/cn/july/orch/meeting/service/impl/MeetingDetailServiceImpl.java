package cn.july.orch.meeting.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.july.core.exception.BusinessException;
import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.po.*;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.*;
import cn.july.orch.meeting.service.FileDetailService;
import cn.july.orch.meeting.service.MeetingDetailService;
import cn.july.orch.meeting.service.TranscriptParserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会议详情查询服务实现
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingDetailServiceImpl implements MeetingDetailService {

    private final NewMeetingMapper newMeetingMapper;
    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingAnalysisReportMapper meetingAnalysisReportMapper;
    private final MeetingMinuteMapper meetingMinuteMapper;
    private final TaskMapper taskMapper;
    private final TaskListMapper taskListMapper;
    private final TranscriptParserService transcriptParserService;
    private final FileDetailService fileDetailService;
    private final FileStorageService fileStorageService;
    private final FeishuAppClient feishuAppClient;
    private final UserInfoAssembler userInfoAssembler;

    @Override
    public MeetingDetailDTO getMeetingDetail(Long meetingId) {
        if (meetingId == null) {
            throw new BusinessException("会议ID不能为空");
        }

        // 1. 查询会议基本信息
        NewMeetingPO meetingPO = newMeetingMapper.selectById(meetingId);
        if (meetingPO == null) {
            throw new BusinessException("会议不存在");
        }

        MeetingDetailDTO detailDTO = buildBasicInfo(meetingPO);

        // 2. 查询会议议程（从会议标准表中获取）
        if (meetingPO.getMeetingStandardId() != null) {
            detailDTO.setAgendaItems(getAgendaItems(meetingPO.getMeetingStandardId()));
        }

        if (meetingPO.getMeetingTags() != null) {
            detailDTO.setMeetingTags(meetingPO.getMeetingTags());
        }

        // 3. 查询会前文档并补充临时URL
        List<PreMeetingDocumentDTO> docs = getPreMeetingDocuments(meetingPO);
        enrichPreDocsWithUrl(docs);
        detailDTO.setPreMeetingDocuments(docs);


        // 4. 参会人详情：未开始取 attendees；已结束取 actualAttendees
        detailDTO.setAttendeeDetails(buildAttendeeDetails(meetingPO));

        // 5. 查询AI纪要
        MeetingAnalysisReportPO reportPO = getAiTranscript(meetingId);
        if (reportPO != null) {
            detailDTO.setAiTranscriptMd(reportPO.getAiTranscriptMd());
        }

        // 6. 查询会议文字稿并解析
        MeetingMinutePO minutePO = meetingMinuteMapper.selectByMeetingId(meetingId);
        if (minutePO != null && minutePO.getMinuteText() != null) {
            detailDTO.setMeetingTranscript(transcriptParserService.parse(minutePO.getMinuteText()));
        }

        // 7. 查询关联任务
        detailDTO.setRelatedTasks(getRelatedTasks(meetingId));

        return detailDTO;
    }

    /**
     * 构建会议基本信息
     */
    private MeetingDetailDTO buildBasicInfo(NewMeetingPO meetingPO) {
        return MeetingDetailDTO.builder()
                .id(meetingPO.getId())
                .meetingName(meetingPO.getMeetingName())
                .meetingDescription(meetingPO.getMeetingDescription())
                .startTime(meetingPO.getStartTime())
                .endTime(meetingPO.getEndTime())
                .status(meetingPO.getStatus())
//                .priorityLevel(meetingPO.getPriorityLevel())
//                .meetingLocation(meetingPO.getMeetingLocation())
                .meetingRoomId(meetingPO.getMeetingRoomId())
                .hostUserId(meetingPO.getHostUserId())
                .recorderUserId(meetingPO.getRecorderUserId())
                .meetingUrl(meetingPO.getMeetingUrl())
                .build();
    }

    /**
     * 获取会议议程（从会议标准中获取）
     */
    private List<AgendaItemDTO> getAgendaItems(Long standardId) {
        MeetingStandardPO standardPO = meetingStandardMapper.selectById(standardId);
        if (standardPO != null && standardPO.getAgendaPlan() != null) {
            List<AgendaItemDTO> agendaItems = standardPO.getAgendaPlan();
            // 按照sequence正序排序，sequence越小越靠前
            return agendaItems.stream()
                    .sorted(Comparator.comparing(AgendaItemDTO::getSequence, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取会前文档
     */
    private List<PreMeetingDocumentDTO> getPreMeetingDocuments(NewMeetingPO meetingPO) {
        if (meetingPO.getPreMeetingDocuments() != null) {
            return meetingPO.getPreMeetingDocuments();
        }
        return new ArrayList<>();
    }

    /**
     * 为会前资料生成临时访问链接
     */
    private void enrichPreDocsWithUrl(List<PreMeetingDocumentDTO> docs) {
        if (docs == null || docs.isEmpty()) {
            return;
        }
        for (PreMeetingDocumentDTO doc : docs) {
            try {
                if (doc == null || doc.getFileKey() == null) {
                    continue;
                }
                FileDetailPO fileDetailPO = fileDetailService.getById(doc.getFileKey());
                if (fileDetailPO == null || fileDetailPO.getUrl() == null) {
                    continue;
                }
                FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                String presignedUrl = fileStorageService.generatePresignedUrl(
                        fileInfo,
                        DateUtil.offsetMinute(new Date(), 60)
                );
                doc.setUrl(presignedUrl);
            } catch (Exception e) {
                // 不中断主流程
            }
        }
    }

    /**
     * 构建参会人员详情：未开始取 attendees；已结束取 actualAttendees
     */
    private List<FSUserInfoDTO> buildAttendeeDetails(NewMeetingPO meetingPO) {
        List<String> openIds;
        LocalDateTime now = LocalDateTime.now();
        if (meetingPO.getEndTime() != null && meetingPO.getEndTime().isBefore(now)) {
            openIds = meetingPO.getActualAttendees();
        } else {
            openIds = meetingPO.getAttendees();
        }
        if (openIds == null || openIds.isEmpty()) {
            return new ArrayList<>();
        }
        try {
            List<User> users = feishuAppClient.getContactService().userBatch(openIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, u -> u));
            return openIds.stream()
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .map(userInfoAssembler::user2DTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 获取AI纪要
     */
    private MeetingAnalysisReportPO getAiTranscript(Long meetingId) {
        LambdaQueryWrapper<MeetingAnalysisReportPO> wrapper = Wrappers.lambdaQuery(MeetingAnalysisReportPO.class);
        wrapper.eq(MeetingAnalysisReportPO::getMeetingId, meetingId);
        wrapper.orderByDesc(MeetingAnalysisReportPO::getCreateTime);
        wrapper.last("limit 1");
        return meetingAnalysisReportMapper.selectOne(wrapper);
    }

    /**
     * 获取关联任务
     */
    private Map<TaskStatusEnum, List<TaskInfoDTO>> getRelatedTasks(Long meetingId) {
        List<TaskPO> tasks = taskMapper.findByMeetingId(meetingId);
        if (tasks == null || tasks.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<TaskStatusEnum, List<TaskInfoDTO>> taskMap = new HashMap<>();
        
        // 按照任务状态分组
        Map<TaskStatusEnum, List<TaskPO>> groupedTasks = tasks.stream()
                .collect(Collectors.groupingBy(TaskPO::getStatus));
        
        // 待办任务
        List<TaskPO> todoTasks = groupedTasks.getOrDefault(TaskStatusEnum.NOT_STARTED, Collections.emptyList());
        if (!todoTasks.isEmpty()) {
            taskMap.put(TaskStatusEnum.NOT_STARTED, convertToTaskInfoDTOs(todoTasks));
        }
        
        // 进行中任务
        List<TaskPO> inProgressTasks = groupedTasks.getOrDefault(TaskStatusEnum.IN_PROGRESS, Collections.emptyList());
        if (!inProgressTasks.isEmpty()) {
            taskMap.put(TaskStatusEnum.IN_PROGRESS, convertToTaskInfoDTOs(inProgressTasks));
        }
        
        // 已完成任务
        List<TaskPO> completedTasks = groupedTasks.getOrDefault(TaskStatusEnum.COMPLETED, Collections.emptyList());
        if (!completedTasks.isEmpty()) {
            taskMap.put(TaskStatusEnum.COMPLETED, convertToTaskInfoDTOs(completedTasks));
        }
        
        // 已逾期任务
        List<TaskPO> cancelledTasks = groupedTasks.getOrDefault(TaskStatusEnum.OVERDUE, Collections.emptyList());
        if (!cancelledTasks.isEmpty()) {
            taskMap.put(TaskStatusEnum.OVERDUE, convertToTaskInfoDTOs(cancelledTasks));
        }
        
        return taskMap;
    }

    /**
     * 转换为任务信息DTO
     */
    private List<TaskInfoDTO> convertToTaskInfoDTOs(List<TaskPO> tasks) {
        LocalDateTime now = LocalDateTime.now();
        
        // 收集所有任务清单ID
        Set<Long> taskListIds = tasks.stream()
                .map(TaskPO::getTaskListId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 批量查询任务清单名称
        final Map<Long, String> taskListNameMap = new HashMap<>();
        if (!taskListIds.isEmpty()) {
            List<TaskListPO> taskListPOs = taskListMapper.selectBatchIds(taskListIds);
            taskListPOs.forEach(taskListPO -> taskListNameMap.put(taskListPO.getId(), taskListPO.getName()));
        }
        
        return tasks.stream().map(task -> {
            boolean isOverdue = task.getDueDate() != null && 
                                task.getStatus() != TaskStatusEnum.COMPLETED &&
                                task.getDueDate().isBefore(now);
            
            return TaskInfoDTO.builder()
                    .id(task.getId())
                    .title(task.getTitle())
                    .description(task.getDescription())
                    .status(task.getStatus())
                    .priority(task.getPriority())
                    .ownerOpenId(task.getOwnerOpenId())
                    .ownerName(task.getOwnerName())
                    .dueDate(task.getDueDate())
                    .isOverdue(isOverdue)
                    .createTime(task.getCreateTime())
                    .taskListId(task.getTaskListId())
                    .taskListName(taskListNameMap.get(task.getTaskListId()))
                    .build();
        }).collect(Collectors.toList());
    }
}