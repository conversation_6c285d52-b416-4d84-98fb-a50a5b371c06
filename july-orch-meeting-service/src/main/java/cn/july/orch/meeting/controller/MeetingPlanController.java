package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingPlanDTO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.service.MeetingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划控制器
 * @date 2025-01-24
 */
@Api(tags = "会议规划管理")
@RestController
@RequestMapping("/meeting-plan")
@RequiredArgsConstructor
public class MeetingPlanController {

    private final MeetingPlanService meetingPlanService;

    @PostMapping("/create")
    @ApiOperation("创建会议规划")
    public void createMeetingPlan(@Valid @RequestBody MeetingPlanCreateCommand command) {
        meetingPlanService.createMeetingPlan(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新会议规划")
    public void updateMeetingPlan(@Valid @RequestBody MeetingPlanUpdateCommand command) {
        meetingPlanService.updateMeetingPlan(command);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除会议规划")
    public void deleteMeetingPlan(@PathVariable("id") Long id) {
        meetingPlanService.deleteMeetingPlan(id);
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询会议规划详情")
    public MeetingPlanDTO getById(@PathVariable("id") Long id) {
        return meetingPlanService.getById(id);
    }

    @PostMapping("/calendar")
    @ApiOperation("查询日历维度的会议规划")
    public List<MeetingPlanDTO> queryCalendar(@RequestBody MeetingPlanCalendarQuery query) {
        return meetingPlanService.queryCalendar(query);
    }

    /**
     * 查询未完成的会议,重复会议也只查询一次
     */
    @PostMapping("/notStartMeeting")
    @ApiOperation("查询未开始会议列表")
    public List<MeetingPlanDTO> notStartMeeting(@RequestBody MeetingPlanCalendarQuery query){
        return meetingPlanService.notStartMeeting(query);
    }
}
