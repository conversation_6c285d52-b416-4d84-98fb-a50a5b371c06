package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingQuery;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.service.NewMeetingActionService;
import cn.july.orch.meeting.service.NewMeetingQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 新会议控制器
 * @date 2025-01-24
 */
@Api(tags = "新会议管理")
@RestController
@RequestMapping("/new-meeting")
@RequiredArgsConstructor
public class NewMeetingController {

    private final NewMeetingQueryService newMeetingQueryService;
    private final NewMeetingActionService newMeetingActionService;

    @PostMapping("/create")
    @ApiOperation("创建会议")
    public void create(@Valid @RequestBody NewMeetingCreateCommand command) {
        newMeetingActionService.createMeeting(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新会议")
    public void update(@Valid @RequestBody NewMeetingUpdateCommand command) {
        newMeetingActionService.updateMeeting(command);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除会议")
    public void delete(@PathVariable("id") Long id) {
        newMeetingActionService.deleteMeeting(id);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("查询会议详情")
    public NewMeetingDTO detail(@PathVariable("id") Long id) {
        return newMeetingQueryService.getById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询会议")
    public PageResultDTO<NewMeetingListDTO> page(@RequestBody NewMeetingQuery query) {
        return newMeetingQueryService.queryPage(query);
    }

    @PostMapping("/status/{id}")
    @ApiOperation("更新会议状态")
    public void updateStatus(@PathVariable("id") Long id, @RequestParam("status") NewMeetingStatusEnum status) {
        newMeetingActionService.updateMeetingStatus(id, status);
    }

    @GetMapping("/by-fs-calendar-event/{fsCalendarEventId}")
    @ApiOperation("根据飞书日程事件ID查询会议")
    public NewMeetingDTO findByFsCalendarEventId(@PathVariable("fsCalendarEventId") String fsCalendarEventId) {
        return newMeetingQueryService.findByFsCalendarEventId(fsCalendarEventId);
    }

    @GetMapping("/by-fs-meeting/{fsMeetingId}")
    @ApiOperation("根据飞书会议ID查询会议")
    public NewMeetingDTO findByFsMeetingId(@PathVariable("fsMeetingId") String fsMeetingId) {
        return newMeetingQueryService.findByFsMeetingId(fsMeetingId);
    }
}
