package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室DTO类
 */
@Data
public class MeetingRoomDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 飞书会议室ID (room_id)
     */
    private String fsRoomId;

    /**
     * 会议室名称
     */
    private String name;

    /**
     * 会议室能容纳的人数
     */
    private Integer capacity;

    /**
     * 会议室的相关描述
     */
    private String description;

    /**
     * 设施信息列表 (同步自飞书的device结构)
     */
    private List<String> devices;

    /**
     * 状态 (0-空闲中, 1-使用中, 2-已预定)
     */
    private MeetingRoomStatusEnum status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}