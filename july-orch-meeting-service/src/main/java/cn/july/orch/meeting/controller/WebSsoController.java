package cn.july.orch.meeting.controller;

import cn.hutool.core.util.StrUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.SignatureModel;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.domain.command.LogoutToolCommand;
import cn.july.orch.meeting.domain.dto.UserTokenDTO;
import cn.july.orch.meeting.domain.query.JsTicketQuery;
import cn.july.orch.meeting.domain.query.SsoUserLoginQuery;
import cn.july.orch.meeting.domain.query.SsoUserTokenQuery;
import cn.july.orch.meeting.service.SsoActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "免登相关")
@RestController
@RequestMapping("/sso/web")
public class WebSsoController {

    @Resource
    private SsoActionService ssoActionService;
    @Resource
    private FeishuAppClient feishuAppClient;

    @GetMapping("/getCode")
    @ApiOperation("获取授权码-测试接口")
    public String getCode(@RequestParam("code") String code){
        log.info("授权码:{}",code);
        return code;
    }

    @PostMapping("/getUserToken")
    @ApiOperation("免登过程-获取token")
    public UserTokenDTO getUserToken(@Validated @RequestBody SsoUserLoginQuery query) {
        return ssoActionService.getUserToken(query);
    }

    @PostMapping("/getJsTicket")
    @ApiOperation("获取js_sdk_ticket")
    public SignatureModel getJsTicket(@Validated @RequestBody JsTicketQuery query) {
        return feishuAppClient.getAuthService().generateSignature(query.getUrl());
    }

    @PostMapping("/getUserInfo")
    @ApiOperation("获取用户信息")
    public UserInfoDTO getUserInfo(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            query.setToken(CurrentUserHolder.getToken());
        }
        UserInfoDTO userInfo = ssoActionService.getUserInfo(query.getToken());
        userInfo.setAuthConfig(ssoActionService.getAuthConfig(userInfo.getOpenId()));
        ssoActionService.getResource(userInfo);
        return userInfo;
    }

    @PostMapping("/refreshToken")
    @ApiOperation("刷新token")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoActionService.refreshToken(query);
    }

    @PostMapping("/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoActionService.logout(query.getToken());
    }

    @PostMapping("/logoutTool")
    @ApiOperation("登出工具")
    public Boolean logoutTool(@RequestBody LogoutToolCommand command) {
        return ssoActionService.logoutTool(command);
    }

}


