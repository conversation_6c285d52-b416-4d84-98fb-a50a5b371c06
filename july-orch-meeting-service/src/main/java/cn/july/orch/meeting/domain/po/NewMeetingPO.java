package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.database.mybatisplus.typehandler.ListStringTypeHandler;
import cn.july.orch.meeting.config.PreMeetingDocumentListTypeHandler;
import cn.july.orch.meeting.config.SimpleMeetingTagListTypeHandler;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议PO
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName(value = "new_meeting", autoResultMap = true)
public class NewMeetingPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议名称
     */
    @TableField("meeting_name")
    private String meetingName;

    /**
     * 会议描述
     */
    @TableField("meeting_description")
    private String meetingDescription;

    /**
     * 会议规划ID
     */
    @TableField("meeting_plan_id")
    private Long meetingPlanId;

    /**
     * 会议标准ID
     */
    @TableField("meeting_standard_id")
    private Long meetingStandardId;

    /**
     * 会议编号
     */
    @TableField("meeting_no")
    private String meetingNo;

    /**
     * 会议开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 会议状态
     */
    @TableField("status")
    private NewMeetingStatusEnum status;


    /**
     * 会议室ID
     */
    @TableField("meeting_room_id")
    private Long meetingRoomId;

    /**
     * 参会人员列表（用户ID列表）
     */
    @TableField(value = "attendees", typeHandler = ListStringTypeHandler.class)
    private List<String> attendees;

    /**
     * 主持人ID
     */
    @TableField("host_user_id")
    private String hostUserId;

    /**
     * 记录员ID
     */
    @TableField("recorder_user_id")
    private String recorderUserId;

    /**
     * 飞书日程事件ID
     */
    @TableField("fs_calendar_event_id")
    private String fsCalendarEventId;

    /**
     * 飞书会议ID
     */
    @TableField("fs_meeting_id")
    private String fsMeetingId;

    /**
     * 会议链接
     */
    @TableField("meeting_url")
    private String meetingUrl;

    /**
     * 妙计链接
     */
    @TableField("minute_url")
    private String minuteUrl;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 是否启用会前文档AI汇总
     */
    @TableField("enable_doc_ai_summary")
    private Boolean enableDocAiSummary;

    /**
     * 会议标签列表
     */
    @TableField(value = "meeting_tags_json", typeHandler = SimpleMeetingTagListTypeHandler.class)
    private List<SimpleMeetingTagDTO> meetingTags;

    /**
     * 会前文档列表
     */
    @TableField(value = "pre_meeting_documents", jdbcType = org.apache.ibatis.type.JdbcType.OTHER, typeHandler = PreMeetingDocumentListTypeHandler.class)
    private List<PreMeetingDocumentDTO> preMeetingDocuments;

    /**
     * 实际会议开始时间
     */
    @TableField("actual_start_time")
    private LocalDateTime actualStartTime;

    /**
     * 实际会议结束时间
     */
    @TableField("actual_end_time")
    private LocalDateTime actualEndTime;

    /**
     * 实际参会人员open_id列表
     */
    @TableField(value = "actual_attendees", typeHandler = cn.july.orch.meeting.config.CustomListStringTypeHandler.class)
    private List<String> actualAttendees;
}