package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.dto.TaskAnalysisDTO;
import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.domain.query.TaskAnalysisQuery;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务统计分析查询服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskAnalysisService {

    private final TaskMapper taskMapper;

    /**
     * 获取任务统计分析数据
     * 
     * @param query 查询条件
     * @return 统计分析结果
     */
    public TaskAnalysisDTO getStatistics(TaskAnalysisQuery query) {
        try {
            log.info("开始获取任务统计分析数据，年月：{}，标签数量：{}，是否使用模拟数据：{}", 
                query.getYearMonth(), 
                query.getMeetingTagIds() != null ? query.getMeetingTagIds().size() : 0,
                query.getUseMockData());
            
            // 如果启用模拟数据，直接返回模拟的统计数据
            if (Boolean.TRUE.equals(query.getUseMockData())) {
                log.info("使用模拟数据生成任务统计结果");
                return buildMockStatisticsResult();
            }

            // 解析年月参数
            YearMonth yearMonth = YearMonth.parse(query.getYearMonth());
            LocalDateTime startTime = yearMonth.atDay(1).atStartOfDay();
            LocalDateTime endTime = yearMonth.atEndOfMonth().atTime(23, 59, 59);

            // 根据条件查询任务数据
            List<TaskPO> tasks = queryTasksByConditions(query, startTime, endTime);
            log.info("查询到符合条件的任务数量：{}", tasks.size());

            // 构建统计结果
            return buildStatisticsResult(tasks, startTime, endTime, query);

        } catch (Exception e) {
            log.error("获取任务统计分析数据失败", e);
            throw new BusinessException("获取任务统计分析数据失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询任务数据
     */
    private List<TaskPO> queryTasksByConditions(TaskAnalysisQuery query, 
                                               LocalDateTime startTime, 
                                               LocalDateTime endTime) {
        LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();
        
        // 时间范围筛选（创建时间在查询月份内）
        queryWrapper.between(TaskPO::getCreateTime, startTime, endTime);
        
        // 逻辑删除筛选
        queryWrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
        
        List<TaskPO> tasks = taskMapper.selectList(queryWrapper);
        
        // 如果指定了会议标签，进行标签筛选
        if (query.getMeetingTagIds() != null && !query.getMeetingTagIds().isEmpty()) {
            return filterTasksByMeetingTags(tasks, query.getMeetingTagIds());
        }
        
        return tasks;
    }
    
    /**
     * 根据会议标签筛选任务
     */
    private List<TaskPO> filterTasksByMeetingTags(List<TaskPO> tasks, List<Long> tagIds) {
        return tasks.stream()
            .filter(task -> {
                List<SimpleMeetingTagDTO> meetingTags = task.getMeetingTags();
                if (meetingTags == null || meetingTags.isEmpty()) {
                    return false;
                }
                
                // 检查任务的会议标签是否包含任一指定标签
                return meetingTags.stream()
                    .anyMatch(tag -> tagIds.contains(tag.getId()));
            })
            .collect(Collectors.toList());
    }

    /**
     * 构建统计分析结果
     */
    private TaskAnalysisDTO buildStatisticsResult(List<TaskPO> tasks, 
                                                 LocalDateTime startTime, 
                                                 LocalDateTime endTime,
                                                 TaskAnalysisQuery query) {
        return TaskAnalysisDTO.builder()
            .basicStatistics(buildBasicStatistics(tasks, startTime, endTime))
            .taskPhaseStatistics(buildTaskPhaseStatistics(tasks))
            .meetingTypeDistribution(buildMeetingTypeDistribution(tasks))
            .tagOverdueTasksRanking(buildTagOverdueTasksRanking(tasks))
            .taskAgeAnalysis(buildTaskAgeAnalysis(tasks))
            .priorityTasks(buildPriorityTasks(tasks))
            .build();
    }

    /**
     * 构建基础统计信息
     */
    private TaskAnalysisDTO.BasicStatistics buildBasicStatistics(List<TaskPO> tasks, 
                                                                LocalDateTime startTime, 
                                                                LocalDateTime endTime) {
        // 进行中任务数
        int inProgressCount = (int) tasks.stream()
            .filter(task -> TaskStatusEnum.IN_PROGRESS.equals(task.getStatus()))
            .count();
        
        // 已完成任务数
        int completedCount = (int) tasks.stream()
            .filter(task -> TaskStatusEnum.COMPLETED.equals(task.getStatus()))
            .count();
        
        // 总任务数(不包括已完成的)
        int totalActiveTasks = (int) tasks.stream()
            .filter(task -> !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
            .count();
        
        // 任务完成率
        int totalTasks = tasks.size();
        double completionRate = totalTasks > 0 ? (completedCount * 100.0 / totalTasks) : 0.0;
        
        // 逾期任务总数
        LocalDateTime now = LocalDateTime.now();
        int overdueTaskCount = (int) tasks.stream()
            .filter(task -> task.getDueDate() != null && 
                    task.getDueDate().isBefore(now) && 
                    !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
            .count();
        
        // 按时完成率（截止时间小于等于完成时间为超期）
        int onTimeCompletedCount = (int) tasks.stream()
            .filter(task -> TaskStatusEnum.COMPLETED.equals(task.getStatus()) && 
                    task.getCompletedAt() != null && 
                    task.getDueDate() != null && 
                    !task.getCompletedAt().isAfter(task.getDueDate()))
            .count();
        
        double onTimeCompletionRate = completedCount > 0 ? 
            (onTimeCompletedCount * 100.0 / completedCount) : 0.0;
        
        return TaskAnalysisDTO.BasicStatistics.builder()
            .totalCount(inProgressCount)
            .completionRate(Math.round(completionRate * 10.0) / 10.0)
            .overdueTaskCount(overdueTaskCount)
            .onTimeCompletionRate(Math.round(onTimeCompletionRate * 10.0) / 10.0)
            .build();
    }

    /**
     * 构建任务生命周期阶段统计
     */
    private List<TaskAnalysisDTO.TaskPhaseStatistics> buildTaskPhaseStatistics(List<TaskPO> tasks) {
        int totalCount = tasks.size();
        List<TaskAnalysisDTO.TaskPhaseStatistics> results = new ArrayList<>();
        
        // 统计各状态任务数量
        Map<TaskStatusEnum, Long> statusCounts = tasks.stream()
            .collect(Collectors.groupingBy(TaskPO::getStatus, Collectors.counting()));
        
        // 添加各阶段统计
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            long count = statusCounts.getOrDefault(status, 0L);
            double percentage = totalCount > 0 ? (count * 100.0 / totalCount) : 0.0;
            
            results.add(TaskAnalysisDTO.TaskPhaseStatistics.builder()
                .phaseName(status.getDesc())
                .taskCount(Math.toIntExact(count))
                .percentage(Math.round(percentage * 10.0) / 10.0)
                .build());
        }
        
        // 按任务数量降序排序
        results.sort((a, b) -> Integer.compare(b.getTaskCount(), a.getTaskCount()));
        
        return results;
    }

    /**
     * 构建会议类型任务分布
     */
    private List<TaskAnalysisDTO.MeetingTypeDistribution> buildMeetingTypeDistribution(List<TaskPO> tasks) {
        int totalCount = tasks.size();
        List<TaskAnalysisDTO.MeetingTypeDistribution> results = new ArrayList<>();
        Map<String, Integer> typeCount = new HashMap<>();
        
        // 按会议标签类型分组统计
        for (TaskPO task : tasks) {
            List<SimpleMeetingTagDTO> meetingTags = task.getMeetingTags();
            if (meetingTags != null && !meetingTags.isEmpty()) {
                for (SimpleMeetingTagDTO tag : meetingTags) {
                    String tagName = tag.getName();
                    typeCount.put(tagName, typeCount.getOrDefault(tagName, 0) + 1);
                }
            } else {
                // 无标签归为"其他"类别
                typeCount.put("其他", typeCount.getOrDefault("其他", 0) + 1);
            }
        }
        
        // 转换为统计结果
        for (Map.Entry<String, Integer> entry : typeCount.entrySet()) {
            double percentage = totalCount > 0 ? (entry.getValue() * 100.0 / totalCount) : 0.0;
            
            results.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
                .meetingType(entry.getKey())
                .taskCount(entry.getValue())
                .percentage(Math.round(percentage * 10.0) / 10.0)
                .build());
        }
        
        // 按任务数量降序排序
        results.sort((a, b) -> Integer.compare(b.getTaskCount(), a.getTaskCount()));
        
        return results;
    }

    /**
     * 构建不同会议标签下逾期任务排行
     */
    private List<TaskAnalysisDTO.TagOverdueTasksRanking> buildTagOverdueTasksRanking(List<TaskPO> tasks) {
        List<TaskAnalysisDTO.TagOverdueTasksRanking> results = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 按会议标签分组
        Map<String, List<TaskPO>> tasksByTag = new HashMap<>();
        
        // 遍历所有任务并按标签分组
        for (TaskPO task : tasks) {
            List<SimpleMeetingTagDTO> meetingTags = task.getMeetingTags();
            if (meetingTags != null && !meetingTags.isEmpty()) {
                for (SimpleMeetingTagDTO tag : meetingTags) {
                    String tagName = tag.getName();
                    tasksByTag.computeIfAbsent(tagName, k -> new ArrayList<>()).add(task);
                }
            } else {
                // 无标签的任务归为"其他"
                tasksByTag.computeIfAbsent("其他", k -> new ArrayList<>()).add(task);
            }
        }
        
        // 计算每个标签的逾期任务数量和比例
        for (Map.Entry<String, List<TaskPO>> entry : tasksByTag.entrySet()) {
            String tagName = entry.getKey();
            List<TaskPO> tagTasks = entry.getValue();
            int totalTagTasks = tagTasks.size();
            
            // 计算该标签下的逾期任务数
            int overdueCount = (int) tagTasks.stream()
                .filter(task -> task.getDueDate() != null && 
                        task.getDueDate().isBefore(now) && 
                        !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
                .count();
            
            // 计算逾期率
            double overdueRate = totalTagTasks > 0 ? (overdueCount * 100.0 / totalTagTasks) : 0.0;
            
            results.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
                .tagName(tagName)
                .overdueTaskCount(overdueCount)
                .totalTaskCount(totalTagTasks)
                .overdueRate(Math.round(overdueRate * 10.0) / 10.0)
                .build());
        }
        
        // 按逾期任务数量降序排序
        results.sort((a, b) -> Integer.compare(b.getOverdueTaskCount(), a.getOverdueTaskCount()));
        
        return results;
    }
    
    /**
     * 构建任务账龄分析
     */
    private List<TaskAnalysisDTO.TaskAgeAnalysis> buildTaskAgeAnalysis(List<TaskPO> tasks) {
        List<TaskAnalysisDTO.TaskAgeAnalysis> results = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        int totalTasks = tasks.size();
        
        // 账龄区间定义（天）
        String[] ageRanges = {"0-7天", "8-15天", "16-30天", "31-60天", "61-90天", "90天以上"};
        int[] ageLimits = {0, 7, 15, 30, 60, 90, Integer.MAX_VALUE};
        int[] ageCounts = new int[ageRanges.length];
        
        // 统计各账龄区间的任务数量
        for (TaskPO task : tasks) {
            if (task.getCreateTime() != null) {
                // 计算任务建立至今的天数
                long daysSinceCreation = Duration.between(task.getCreateTime(), now).toDays();
                
                // 分配到相应区间
                for (int i = 0; i < ageLimits.length - 1; i++) {
                    if (daysSinceCreation >= ageLimits[i] && daysSinceCreation < ageLimits[i + 1]) {
                        ageCounts[i]++;
                        break;
                    }
                }
            }
        }
        
        // 构建返回结果
        for (int i = 0; i < ageRanges.length; i++) {
            double percentage = totalTasks > 0 ? (ageCounts[i] * 100.0 / totalTasks) : 0.0;
            
            results.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
                .ageRange(ageRanges[i])
                .taskCount(ageCounts[i])
                .percentage(Math.round(percentage * 10.0) / 10.0)
                .build());
        }
        
        return results;
    }

    /**
     * 构建优先任务列表
     */
    private TaskAnalysisDTO.PriorityTasks buildPriorityTasks(List<TaskPO> tasks) {
        // 筛选高优先级任务
        List<TaskPO> highPriorityTasks = tasks.stream()
            .filter(task -> TaskPriorityEnum.HIGH.equals(task.getPriority()) && 
                    !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
            .sorted(Comparator.comparing(TaskPO::getDueDate, Comparator.nullsLast(Comparator.naturalOrder())))
            .limit(5)
            .collect(Collectors.toList());
        
        // 筛选超期任务
        LocalDateTime now = LocalDateTime.now();
        List<TaskPO> overdueTasks = tasks.stream()
            .filter(task -> task.getDueDate() != null && 
                    task.getDueDate().isBefore(now) && 
                    !TaskStatusEnum.COMPLETED.equals(task.getStatus()))
            .sorted(Comparator.comparing(TaskPO::getDueDate, Comparator.nullsLast(Comparator.naturalOrder())))
            .limit(5)
            .collect(Collectors.toList());
        
        // 转换为DTO
        List<TaskAnalysisDTO.PriorityTaskItem> highPriorityItems = convertToPriorityTaskItems(highPriorityTasks);
        List<TaskAnalysisDTO.PriorityTaskItem> overdueItems = convertToPriorityTaskItems(overdueTasks);
        
        return TaskAnalysisDTO.PriorityTasks.builder()
            .highPriorityTasks(highPriorityItems)
            .overdueTasks(overdueItems)
            .build();
    }
    
    /**
     * 将任务PO转换为优先任务项DTO
     */
    private List<TaskAnalysisDTO.PriorityTaskItem> convertToPriorityTaskItems(List<TaskPO> tasks) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        return tasks.stream()
            .map(task -> TaskAnalysisDTO.PriorityTaskItem.builder()
                .id(task.getId())
                .title(task.getTitle())
                .meetingTags(task.getMeetingTags() != null ? task.getMeetingTags() : new ArrayList<>())
                .priority(task.getPriority().getCode())
                .priorityName(task.getPriority().getDesc())
                .ownerName(task.getOwnerName())
                .dueDate(task.getDueDate() != null ? task.getDueDate().format(formatter) : "无截止日期")
                .build())
            .collect(Collectors.toList());
    }

    /**
     * 生成模拟的统计数据，用于系统无数据时的图表展示
     * 
     * @return 模拟的统计数据
     */
    private TaskAnalysisDTO buildMockStatisticsResult() {
        // 构建基础统计信息
        TaskAnalysisDTO.BasicStatistics basicStatistics = TaskAnalysisDTO.BasicStatistics.builder()
            .totalCount(36)  // 进行中任务数
            .completionRate(65.0)
            .overdueTaskCount(24)  // 逾期任务总数
            .onTimeCompletionRate(78.0)
            .build();
        
        // 构建任务生命周期阶段统计
        List<TaskAnalysisDTO.TaskPhaseStatistics> taskPhaseStatistics = new ArrayList<>();
        taskPhaseStatistics.add(TaskAnalysisDTO.TaskPhaseStatistics.builder()
            .phaseName("已完成")
            .taskCount(96)
            .percentage(65.0)
            .build());
        taskPhaseStatistics.add(TaskAnalysisDTO.TaskPhaseStatistics.builder()
            .phaseName("进行中")
            .taskCount(36)
            .percentage(24.3)
            .build());
        taskPhaseStatistics.add(TaskAnalysisDTO.TaskPhaseStatistics.builder()
            .phaseName("未开始")
            .taskCount(14)
            .percentage(9.4)
            .build());
        taskPhaseStatistics.add(TaskAnalysisDTO.TaskPhaseStatistics.builder()
            .phaseName("已超期")
            .taskCount(2)
            .percentage(1.3)
            .build());
        
        // 构建会议类型任务分布
        List<TaskAnalysisDTO.MeetingTypeDistribution> meetingTypeDistribution = new ArrayList<>();
        meetingTypeDistribution.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
            .meetingType("战略规划类")
            .taskCount(42)
            .percentage(28.4)
            .build());
        meetingTypeDistribution.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
            .meetingType("客户与市场")
            .taskCount(38)
            .percentage(25.7)
            .build());
        meetingTypeDistribution.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
            .meetingType("产品研发")
            .taskCount(36)
            .percentage(24.3)
            .build());
        meetingTypeDistribution.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
            .meetingType("运营管理")
            .taskCount(18)
            .percentage(12.2)
            .build());
        meetingTypeDistribution.add(TaskAnalysisDTO.MeetingTypeDistribution.builder()
            .meetingType("人力资源")
            .taskCount(14)
            .percentage(9.4)
            .build());
        
        // 构建不同会议标签下逾期任务排行
        List<TaskAnalysisDTO.TagOverdueTasksRanking> tagOverdueTasksRanking = new ArrayList<>();
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("战略经营类")
            .overdueTaskCount(8)
            .totalTaskCount(42)
            .overdueRate(19.0)
            .build());
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("客户与市场")
            .overdueTaskCount(6)
            .totalTaskCount(38)
            .overdueRate(15.8)
            .build());
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("产品研发")
            .overdueTaskCount(4)
            .totalTaskCount(36)
            .overdueRate(11.1)
            .build());
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("技术研发")
            .overdueTaskCount(3)
            .totalTaskCount(18)
            .overdueRate(16.7)
            .build());
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("财务审计")
            .overdueTaskCount(2)
            .totalTaskCount(14)
            .overdueRate(14.3)
            .build());
        tagOverdueTasksRanking.add(TaskAnalysisDTO.TagOverdueTasksRanking.builder()
            .tagName("人力资源")
            .overdueTaskCount(1)
            .totalTaskCount(10)
            .overdueRate(10.0)
            .build());
        
        // 构建任务账龄分析
        List<TaskAnalysisDTO.TaskAgeAnalysis> taskAgeAnalysis = new ArrayList<>();
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("0-7天")
            .taskCount(42)
            .percentage(28.4)
            .build());
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("8-15天")
            .taskCount(38)
            .percentage(25.7)
            .build());
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("16-30天")
            .taskCount(26)
            .percentage(17.6)
            .build());
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("31-60天")
            .taskCount(18)
            .percentage(12.2)
            .build());
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("61-90天")
            .taskCount(12)
            .percentage(8.1)
            .build());
        taskAgeAnalysis.add(TaskAnalysisDTO.TaskAgeAnalysis.builder()
            .ageRange("90天以上")
            .taskCount(8)
            .percentage(5.4)
            .build());
        
        // 构建优先任务列表
        // 高优先级任务列表
        List<TaskAnalysisDTO.PriorityTaskItem> highPriorityTasks = new ArrayList<>();
        highPriorityTasks.add(buildMockPriorityTaskItem(1001L, "产品品网站UI改版设计", "2025-09-05", 2, "高", "张设计", "战略规划类"));
        highPriorityTasks.add(buildMockPriorityTaskItem(1002L, "Q3市场推广产品预算", "2025-09-10", 2, "高", "李市场", "客户与市场"));
        highPriorityTasks.add(buildMockPriorityTaskItem(1003L, "核心服务性能优化", "2025-09-12", 2, "高", "王研发", "产品研发"));
        highPriorityTasks.add(buildMockPriorityTaskItem(1004L, "年度财务报表审核", "2025-09-15", 2, "高", "赵财务", "运营管理"));
        highPriorityTasks.add(buildMockPriorityTaskItem(1005L, "招聘流程优化方案", "2025-09-20", 2, "高", "钱人事", "人力资源"));
        
        // 超期任务列表
        List<TaskAnalysisDTO.PriorityTaskItem> overdueTasks = new ArrayList<>();
        overdueTasks.add(buildMockPriorityTaskItem(2001L, "周度项目进度同步会", "2025-08-15", 1, "中", "刘项目", "运营管理"));
        overdueTasks.add(buildMockPriorityTaskItem(2002L, "新员工入职培训会议", "2025-08-17", 1, "中", "孙培训", "人力资源"));
        overdueTasks.add(buildMockPriorityTaskItem(2003L, "市场活动策划讨论会", "2025-08-20", 1, "中", "周市场", "客户与市场"));
        overdueTasks.add(buildMockPriorityTaskItem(2004L, "部门建设活动策划会", "2025-08-22", 0, "低", "吴组织", "人力资源"));
        overdueTasks.add(buildMockPriorityTaskItem(2005L, "客户投诉处理流程优化", "2025-08-25", 1, "中", "郑客服", "客户与市场"));
        
        TaskAnalysisDTO.PriorityTasks priorityTasks = TaskAnalysisDTO.PriorityTasks.builder()
            .highPriorityTasks(highPriorityTasks)
            .overdueTasks(overdueTasks)
            .build();
        
        // 构建并返回最终的统计数据
        return TaskAnalysisDTO.builder()
            .basicStatistics(basicStatistics)
            .taskPhaseStatistics(taskPhaseStatistics)
            .meetingTypeDistribution(meetingTypeDistribution)
            .tagOverdueTasksRanking(tagOverdueTasksRanking)
            .taskAgeAnalysis(taskAgeAnalysis)
            .priorityTasks(priorityTasks)
            .build();
    }
    
    /**
     * 构建模拟的优先任务项
     */
    private TaskAnalysisDTO.PriorityTaskItem buildMockPriorityTaskItem(
            Long id, String title, String dueDate, Integer priority, 
            String priorityName, String ownerName, String tagName) {
        List<SimpleMeetingTagDTO> tags = new ArrayList<>();
        tags.add(SimpleMeetingTagDTO.builder()
            .id(id % 10)
            .name(tagName)
            .color(getRandomColor())
            .build());
        
        return TaskAnalysisDTO.PriorityTaskItem.builder()
            .id(id)
            .title(title)
            .meetingTags(tags)
            .priority(priority)
            .priorityName(priorityName)
            .ownerName(ownerName)
            .dueDate(dueDate)
            .build();
    }
    
    /**
     * 生成随机颜色代码，用于模拟数据的标签颜色
     */
    private String getRandomColor() {
        String[] colors = {
            "#3498DB", "#2ECC71", "#F1C40F", "#E74C3C", "#9B59B6",
            "#1ABC9C", "#E67E22", "#34495E", "#27AE60", "#D35400"
        };
        return colors[(int)(Math.random() * colors.length)];
    }
}