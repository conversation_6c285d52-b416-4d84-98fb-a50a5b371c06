package cn.july.orch.meeting.properties;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 智能体应用配置
 * @date 2025-04-09
 */
@Data
public class AgentProperties {

    /**
     * 智能体调用域名
     */
    private String invokeDomain = "https://cerebro-sit.genn.cn";

    /**
     * 内部鉴权
     */
    private String authorization = "Bearer gennai-internal-api-key-4vQYhr7VPLTQyV9X4GRPQgmRrDg4Kb7qsdGP";

    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;

    /**
     * 高频问题统计
     */
    private String highFreApiKey = "gennai-uIePn9MMUaptejVxEXrWVlnsPtyMdyIRYR1xqA5bxiVVDDxyHBRntYyt";

    /**
     * 知识缺失分析
     */
    private String gapAnlApiKey = "gennai-xqS9TnK5uy6EvXBcp7wXinUBQYVupJwtMvXHKJvjmYDz2bvaa9VLNX";

    // ==================== 智能体应用ID配置 ====================
    
    /**
     * 会议分析智能体应用ID
     */
    private String meetingAnalysisAppId = "6892ae7c9e9b7bc7598f5c54";

    /**
     * AI智能纪要智能体应用ID
     */
    private String aiTranscriptAppId = "68ad718ca364b37b2d3b5a53";

    /**
     * 问答助手应用ID
     */
    private String qaAppId = "688c63f47e9027870e39c7dd";

    /**
     * 文件汇总应用ID
     */
    private String fileSummaryAppId = "688c63597e9027870e39c5d9";

    // ==================== 智能体鉴权配置 ====================
    
    /**
     * 文件汇总鉴权
     */
    private String summaryAuthorization = "Bearer gennai-yRjuiUeSsiRQ2IXwke4UrWkpFx9zKBmrgh2yhLaOM2zpvTVNb5w4vqdje";

    /**
     * 问答助手鉴权
     */
    private String qaAuthorization = "Bearer gennai-iFJdQucHdmkZijiNRhvmjYC6vf1oIALgqPdjFbSE59bTUpsZJ8RhOQN";

    /**
     * 会议报告鉴权
     */
    private String reportAuthorization = "Bearer gennai-eXd1B6Hty4d70trGu3CuIGGkfSS2CMNYFkyHsbUYwY4WiftvxNoU56sYoh";

    /**
     * 会议纪要鉴权
     */
    private String aiTranscriptAuthorization = "Bearer gennai-uJELy30V6ttN6ZbiDAbClCOEoZh4mGOMrW6XzfkAeaku48l69OxbRO3YXANjkK";
}
