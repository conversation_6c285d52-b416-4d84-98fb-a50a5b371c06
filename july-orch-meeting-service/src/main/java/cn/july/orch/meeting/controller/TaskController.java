package cn.july.orch.meeting.controller;

import cn.july.core.model.ddd.IdQuery;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.TaskCreateCommand;
import cn.july.orch.meeting.domain.command.TaskUpdateCommand;
import cn.july.orch.meeting.domain.command.TaskDecomposeCommand;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.query.TaskQuery;
import cn.july.orch.meeting.service.TaskActionService;
import cn.july.orch.meeting.service.TaskQueryService;
import cn.july.orch.meeting.service.TaskSupervisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务控制器
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/task")
public class TaskController {

    @Resource
    private TaskQueryService taskQueryService;
    @Resource
    private TaskActionService taskActionService;
    @Resource
    private TaskSupervisionService taskSupervisionService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询任务列表")
    public PageResultDTO<TaskListDTO> page(@RequestBody TaskQuery taskQuery) {
        return taskQueryService.page(taskQuery);
    }

    @PostMapping("/nested-page")
    @ApiOperation(value = "分页查询任务嵌套列表", notes = "仅显示一级任务，子任务作为数组显示，支持统计信息")
    public PageResultDTO<TaskListDTO> nestedPage(@RequestBody TaskQuery taskQuery) {
        return taskQueryService.nestedPage(taskQuery);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询任务详情")
    public TaskDTO detail(@RequestBody IdQuery idQuery) {
        return taskQueryService.detail(idQuery);
    }

    @PostMapping("/detail-with-activities")
    @ApiOperation(value = "查询任务详情含动态", notes = "返回任务的基础信息、子任务完成情况及任务动态")
    public TaskDTO detailWithActivities(@RequestBody IdQuery idQuery) {
        return taskQueryService.detailWithActivities(idQuery);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建任务", notes = "任务必须归属任务清单，附件和关联会议为可选项")
    public Long create(@Validated @RequestBody TaskCreateCommand command) {
        return taskActionService.create(command);
    }

    @PostMapping("/decompose")
    @ApiOperation(value = "拆解任务", notes = "支持同时拆解多个子任务")
    public List<Long> decompose(@Validated @RequestBody TaskDecomposeCommand command) {
        return taskActionService.decompose(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新任务")
    public void update(@Validated @RequestBody TaskUpdateCommand command) {
        taskActionService.update(command);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除任务")
    public void delete(@RequestBody IdQuery idQuery) {
        taskActionService.delete(idQuery);
    }

    @PostMapping("/complete")
    @ApiOperation(value = "完成任务")
    public void complete(@RequestBody IdQuery idQuery) {
        taskActionService.complete(idQuery);
    }

    @PostMapping("/start")
    @ApiOperation(value = "开始任务")
    public void start(@RequestBody IdQuery idQuery) {
        taskActionService.start(idQuery);
    }

    @GetMapping("/listByMeetingId")
    @ApiOperation(value = "根据会议ID查询任务列表")
    public List<TaskListDTO> listByMeetingId(@RequestParam Long meetingId) {
        return taskQueryService.listByMeetingId(meetingId);
    }

    @GetMapping("/listByOwner")
    @ApiOperation(value = "根据负责人查询任务列表")
    public List<TaskListDTO> listByOwner(@RequestParam String ownerOpenId) {
        return taskQueryService.listByOwner(ownerOpenId);
    }

    @PostMapping("/myTasks")
    @ApiOperation(value = "查询我的任务列表")
    public PageResultDTO<TaskListDTO> myTasks(@RequestBody TaskQuery taskQuery) {
        return taskQueryService.myTasks(taskQuery);
    }

    @PostMapping("/processOverdue")
    @ApiOperation(value = "处理超期任务")
    public void processOverdue() {
        taskActionService.processOverdueTasks();
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "获取任务统计信息", notes = "获取任务数据统计面板信息，包括总任务数、各状态任务数、完成率等")
    public TaskStatusStatisticsDTO getTaskStatistics() {
        return taskQueryService.getTaskStatistics();
    }

    @GetMapping("/statistics/byTaskList")
    @ApiOperation(value = "获取指定任务清单的任务统计信息", notes = "根据任务清单ID获取任务数据统计面板信息")
    public TaskStatusStatisticsDTO getTaskStatisticsByTaskList(@RequestParam(required = false) Long taskListId) {
        return taskQueryService.getTaskStatistics(taskListId);
    }

    @GetMapping("/supervision/list")
    @ApiOperation(value = "获取督办任务列表", notes = "获取距离截止时间3小时内需要督办的任务列表")
    public List<TaskSupervisionDTO> getTasksForSupervision() {
        return taskQueryService.getTasksForSupervision();
    }

    @PostMapping("/supervision/manual/{taskId}")
    @ApiOperation(value = "手动督办任务", notes = "手动触发指定任务的督办消息发送")
    public void manualSuperviseTask(@PathVariable("taskId") Long taskId) {
        taskSupervisionService.manualSuperviseTask(taskId);
    }

    @PostMapping("/supervision/auto")
    @ApiOperation(value = "自动督办紧急任务", notes = "自动督办所有距离截止时间3小时内的任务（定时任务调用）")
    public void autoSuperviseUrgentTasks() {
        taskSupervisionService.autoSuperviseUrgentTasks();
    }

    @GetMapping("/supervision/{taskId}")
    @ApiOperation(value = "获取任务督办信息", notes = "根据任务ID获取督办相关信息")
    public TaskSupervisionDTO getTaskSupervisionById(@PathVariable("taskId") Long taskId) {
        return taskQueryService.getTaskSupervisionById(taskId);
    }

    @PostMapping("/supervision/test")
    @ApiOperation(value = "测试督办功能", notes = "发送模拟督办卡片消息，用于测试飞书卡片效果")
    public void testSupervision(@RequestParam("openId") String openId) {
        taskSupervisionService.testSupervision(openId);
    }
}
