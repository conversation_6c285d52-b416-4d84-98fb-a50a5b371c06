package cn.july.orch.meeting.mapper;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingPlanMapper extends BaseMapper<MeetingPlanPO> {

    /**
     * 查询所有历史会议规划（包括一次性和重复性）
     * 用于查询当前时间之前的会议数据
     */
    List<MeetingPlanPO> queryAllHistoricalPlans(@Param("query") MeetingPlanCalendarQuery query);

    /**
     * 查询未来的一次性会议规划
     * 用于查询当前时间之后的一次性会议
     */
    List<MeetingPlanPO> queryFutureOneTimePlans(@Param("query") MeetingPlanCalendarQuery query);

    /**
     * 查询未来的重复性会议规划
     * 用于查询当前时间之后的重复性会议
     */
    List<MeetingPlanPO> queryFutureRecurringPlans(@Param("query") MeetingPlanCalendarQuery query);

    default List<MeetingPlanPO> findOverduePlans() {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
                .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
                .lt(MeetingPlanPO::getPlannedStartTime, LocalDateTime.now());
        return selectList(wrapper);
    }

    default void updateStatusByIds(List<Long> ids, MeetingPlanStatusEnum status) {
        LambdaUpdateWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaUpdate(MeetingPlanPO.class)
                .in(MeetingPlanPO::getId, ids)
                .set(MeetingPlanPO::getStatus, status);
        update(wrapper);
    }

    default MeetingPlanPO selectByUuidAndNotStart(String uuid) {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
                .eq(MeetingPlanPO::getUuid, uuid)
                .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
                .last("limit 1");
        return selectOne(wrapper);
    }

    default List<MeetingPlanPO> selectByUuid(String uuid){
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
                .eq(MeetingPlanPO::getUuid, uuid);
        return selectList(wrapper);
    }

    default List<MeetingPlanPO> selectNotStartMeeting(MeetingPlanCalendarQuery query){
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
                .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
                .gt(MeetingPlanPO::getPlannedStartTime, query.getStartDate())
                .lt(MeetingPlanPO::getPlannedStartTime, query.getEndDate())
                .like(StrUtil.isNotBlank(query.getName()), MeetingPlanPO::getPlanName, query.getName())
                .like(ObjUtil.isNotNull(query.getTagId()), MeetingPlanPO::getTagIds, query.getTagId())
                .orderByDesc(MeetingPlanPO::getPlannedStartTime);
        return selectList(wrapper);
    }

}
