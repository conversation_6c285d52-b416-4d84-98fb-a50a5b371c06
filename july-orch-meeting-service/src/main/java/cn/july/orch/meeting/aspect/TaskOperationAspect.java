package cn.july.orch.meeting.aspect;

import cn.july.core.model.ddd.IdQuery;
import cn.july.orch.meeting.domain.command.TaskCreateCommand;
import cn.july.orch.meeting.domain.command.TaskDecomposeCommand;
import cn.july.orch.meeting.domain.command.TaskUpdateCommand;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.entity.TaskInfo;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.service.TaskActivityService;
import cn.july.orch.meeting.service.TaskDomainService;
import cn.july.orch.meeting.service.TaskQueryService;
import cn.july.orch.meeting.feishu.FeishuApiService;
import cn.july.orch.meeting.config.FeishuTaskProperties;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Assistant
 * @description 任务操作切面，自动记录任务动态和发送飞书卡片提醒
 */
@Slf4j
@Aspect
@Component
public class TaskOperationAspect {

    @Resource
    private TaskActivityService taskActivityService;
    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private TaskQueryService taskQueryService;
    @Resource
    private FeishuApiService feishuApiService;
    @Resource
    private FeishuTaskProperties feishuTaskProperties;

    // 用于存储更新前的任务信息，ThreadLocal保证线程安全
    private final ThreadLocal<TaskDTO> oldTaskCache = new ThreadLocal<>();

    /**
     * 任务创建后记录动态和发送卡片提醒
     */
    @AfterReturning(pointcut = "execution(* cn.july.orch.meeting.service.TaskActionService.create(..))", returning = "taskId")
    public void afterTaskCreate(JoinPoint joinPoint, Object taskId) {
        try {
            log.debug("切面捕获任务创建操作，任务ID：{}", taskId);
            
            if (taskId instanceof Long) {
                TaskAgg taskAgg = taskDomainService.findById((Long) taskId);
                if (taskAgg != null && taskAgg.getInfo() != null) {
                    taskActivityService.recordCreateActivity(taskAgg.getInfo());
                    
                    // 发送任务创建卡片提醒
                    TaskDTO taskDTO = taskQueryService.detail(IdQuery.builder().id((Long) taskId).build());
                    sendTaskNotification(taskDTO, "CREATE", null);
                }
            }
        } catch (Exception e) {
            log.warn("记录任务创建动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 任务更新前保存原始信息
     */
    @Before("execution(* cn.july.orch.meeting.service.TaskActionService.update(..))")
    public void beforeTaskUpdate(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof TaskUpdateCommand) {
                TaskUpdateCommand command = (TaskUpdateCommand) args[0];
                if (command.getId() != null) {
                    TaskDTO oldTask = taskQueryService.detail(IdQuery.builder().id(command.getId()).build());
                    oldTaskCache.set(oldTask);
                    log.debug("切面保存任务更新前信息，任务ID：{}", command.getId());
                }
            }
        } catch (Exception e) {
            log.warn("保存任务更新前信息失败", e);
        }
    }

    /**
     * 任务更新后记录动态和发送卡片提醒
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskActionService.update(..))")
    public void afterTaskUpdate(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof TaskUpdateCommand) {
                TaskUpdateCommand command = (TaskUpdateCommand) args[0];
                TaskDTO oldTask = oldTaskCache.get();
                
                if (oldTask != null && command.getId() != null) {
                    log.debug("切面捕获任务更新操作，任务ID：{}", command.getId());
                    
                    // 构建变更字段Map
                    Map<String, Object> changedFields = buildChangedFields(oldTask, command);
                    
                    if (!changedFields.isEmpty()) {
                        taskActivityService.recordInfoUpdateActivity(command.getId(), changedFields);
                        
                        // 特殊处理责任人变更
                        if (changedFields.containsKey("owner")) {
                            taskActivityService.recordAssignUserActivity(
                                command.getId(),
                                oldTask.getOwnerName(),
                                command.getOwnerName()
                            );
                        }
                        
                        // 特殊处理截止时间变更
                        if (changedFields.containsKey("dueDate")) {
                            taskActivityService.recordSetDueDateActivity(
                                command.getId(),
                                oldTask.getDueDate(),
                                command.getDueDate()
                            );
                        }
                        
                        // 发送任务更新卡片提醒
                        TaskDTO updatedTask = taskQueryService.detail(IdQuery.builder().id(command.getId()).build());
                        sendTaskNotification(updatedTask, "UPDATE_INFO", changedFields);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("记录任务更新动态或发送卡片提醒失败", e);
        } finally {
            // 清理ThreadLocal
            oldTaskCache.remove();
        }
    }

    /**
     * 任务删除后记录动态和发送卡片提醒
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskActionService.delete(..))")
    public void afterTaskDelete(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof IdQuery) {
                IdQuery idQuery = (IdQuery) args[0];
                log.debug("切面捕获任务删除操作，任务ID：{}", idQuery.getId());
                
                taskActivityService.recordDeleteActivity(idQuery.getId(), "用户手动删除");
                
                // 由于任务已被删除，需要从参数中获取任务信息发送提醒
                // 这里简化处理，实际应用中可能需要从其他地方获取任务信息
                // 暂时不发送删除提醒
            }
        } catch (Exception e) {
            log.warn("记录任务删除动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 任务完成后记录动态和发送卡片提醒
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskActionService.complete(..))")
    public void afterTaskComplete(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof IdQuery) {
                IdQuery idQuery = (IdQuery) args[0];
                log.debug("切面捕获任务完成操作，任务ID：{}", idQuery.getId());
                
                taskActivityService.recordCompleteActivity(idQuery.getId());
                
                // 发送任务完成卡片提醒
                TaskDTO task = taskQueryService.detail(idQuery);
                if (task != null) {
                    sendTaskNotification(task, "COMPLETE", null);
                }
            }
        } catch (Exception e) {
            log.warn("记录任务完成动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 任务开始后记录动态和发送卡片提醒
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskActionService.start(..))")
    public void afterTaskStart(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof IdQuery) {
                IdQuery idQuery = (IdQuery) args[0];
                log.debug("切面捕获任务开始操作，任务ID：{}", idQuery.getId());
                
                taskActivityService.recordStartActivity(idQuery.getId());
                
                // 发送任务开始卡片提醒
                TaskDTO task = taskQueryService.detail(idQuery);
                if (task != null) {
                    sendTaskNotification(task, "START", null);
                }
            }
        } catch (Exception e) {
            log.warn("记录任务开始动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 任务拆解后记录动态和发送卡片提醒
     */
    @AfterReturning(pointcut = "execution(* cn.july.orch.meeting.service.TaskActionService.decompose(..))", returning = "subTaskIds")
    public void afterTaskDecompose(JoinPoint joinPoint, Object subTaskIds) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof TaskDecomposeCommand && subTaskIds instanceof List) {
                TaskDecomposeCommand command = (TaskDecomposeCommand) args[0];
                @SuppressWarnings("unchecked")
                List<Long> taskIds = (List<Long>) subTaskIds;
                
                log.debug("切面捕获任务拆解操作，父任务ID：{}，子任务数量：{}", command.getParentTaskId(), taskIds.size());
                
                taskActivityService.recordDecomposeActivity(command.getParentTaskId(), taskIds);
                
                // 发送任务拆解卡片提醒
                TaskDTO parentTask = taskQueryService.detail(IdQuery.builder().id(command.getParentTaskId()).build());
                if (parentTask != null) {
                    Map<String, Object> content = new HashMap<>();
                    content.put("subTaskCount", taskIds.size());
                    sendTaskNotification(parentTask, "DECOMPOSE", content);
                    
                    // 为每个子任务发送提醒
                    for (Long subTaskId : taskIds) {
                        TaskDTO subTask = taskQueryService.detail(IdQuery.builder().id(subTaskId).build());
                        if (subTask != null) {
                            sendSubTaskNotification(subTask, parentTask);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("记录任务拆解动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 自动督办任务后记录动态
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskSupervisionService.autoSuperviseUrgentTasks(..))")
    public void afterAutoSupervise(JoinPoint joinPoint) {
        try {
            log.debug("切面捕获自动督办任务操作");
            // 这里可以记录自动督办的总体动态，但具体任务的督办动态在TaskSupervisionService内部处理
        } catch (Exception e) {
            log.warn("记录自动督办任务动态失败", e);
        }
    }

    /**
     * 手动督办任务后记录动态和发送卡片提醒
     */
    @AfterReturning("execution(* cn.july.orch.meeting.service.TaskSupervisionService.manualSuperviseTask(..))")
    public void afterManualSupervise(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof Long) {
                Long taskId = (Long) args[0];
                log.debug("切面捕获手动督办任务操作，任务ID：{}", taskId);
                
                // 获取任务信息
                TaskDTO task = taskQueryService.detail(IdQuery.builder().id(taskId).build());
                if (task != null) {
                    taskActivityService.recordSuperviseActivity(taskId, "手动督办任务", "一般督办");
                    
                    // 发送任务督办卡片提醒
                    Map<String, Object> content = new HashMap<>();
                    content.put("superviseType", "手动督办");
                    sendTaskNotification(task, "SUPERVISE", content);
                }
            }
        } catch (Exception e) {
            log.warn("记录手动督办任务动态或发送卡片提醒失败", e);
        }
    }

    /**
     * 构建变更字段Map
     */
    private Map<String, Object> buildChangedFields(TaskDTO oldTask, TaskUpdateCommand command) {
        Map<String, Object> changedFields = new HashMap<>();
        
        if (!Objects.equals(oldTask.getTitle(), command.getTitle())) {
            Map<String, Object> titleChange = new HashMap<>();
            titleChange.put("old", oldTask.getTitle());
            titleChange.put("new", command.getTitle());
            changedFields.put("title", titleChange);
        }
        
        if (!Objects.equals(oldTask.getDescription(), command.getDescription())) {
            Map<String, Object> descChange = new HashMap<>();
            descChange.put("old", oldTask.getDescription());
            descChange.put("new", command.getDescription());
            changedFields.put("description", descChange);
        }
        
        if (!Objects.equals(oldTask.getOwnerOpenId(), command.getOwnerOpenId())) {
            Map<String, Object> ownerChange = new HashMap<>();
            ownerChange.put("oldOpenId", oldTask.getOwnerOpenId());
            ownerChange.put("oldName", oldTask.getOwnerName());
            ownerChange.put("newOpenId", command.getOwnerOpenId());
            ownerChange.put("newName", command.getOwnerName());
            changedFields.put("owner", ownerChange);
        }
        
        if (!Objects.equals(oldTask.getPriority(), command.getPriority())) {
            Map<String, Object> priorityChange = new HashMap<>();
            priorityChange.put("old", oldTask.getPriority());
            priorityChange.put("new", command.getPriority());
            changedFields.put("priority", priorityChange);
        }
        
        if (!Objects.equals(oldTask.getDueDate(), command.getDueDate())) {
            Map<String, Object> dueDateChange = new HashMap<>();
            dueDateChange.put("old", oldTask.getDueDate());
            dueDateChange.put("new", command.getDueDate());
            changedFields.put("dueDate", dueDateChange);
        }
        
        return changedFields;
    }

    /**
     * 发送任务操作卡片提醒
     *
     * @param task 任务信息
     * @param operationType 操作类型
     * @param content 附加内容（变更信息等）
     */
    private void sendTaskNotification(TaskDTO task, String operationType, Map<String, Object> content) {
        try {
            // 检查是否启用通知
            if (!feishuTaskProperties.isNotificationEnabled()) {
                return;
            }

            // 构造并发送卡片提醒
            String cardContent = TaskCardBuilder.buildTaskOperationCard(task, operationType, content, feishuTaskProperties);
            feishuApiService.sendCard(task.getOwnerOpenId(), cardContent);
            log.info("任务操作卡片提醒已发送，任务ID：{}，操作类型：{}，负责人：{}", task.getId(), operationType, task.getOwnerName());
        } catch (Exception e) {
            log.warn("发送任务操作卡片提醒失败，任务ID：{}，操作类型：{}", task.getId(), operationType, e);
        }
    }

    /**
     * 发送子任务创建卡片提醒
     *
     * @param subTask 子任务信息
     * @param parentTask 父任务信息
     */
    private void sendSubTaskNotification(TaskDTO subTask, TaskDTO parentTask) {
        try {
            // 检查是否启用通知
            if (!feishuTaskProperties.isNotificationEnabled()) {
                return;
            }

            // 构造并发送子任务卡片提醒
            String cardContent = TaskCardBuilder.buildSubTaskCreatedCard(subTask, parentTask, feishuTaskProperties);
            feishuApiService.sendCard(subTask.getOwnerOpenId(), cardContent);
            log.info("子任务创建卡片提醒已发送，子任务ID：{}，负责人：{}", subTask.getId(), subTask.getOwnerName());
        } catch (Exception e) {
            log.warn("发送子任务创建卡片提醒失败，子任务ID：{}", subTask.getId(), e);
        }
    }
}