package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议规划日历查询对象
 * @date 2025-01-24
 */
@Data
public class MeetingPlanCalendarQuery implements Serializable {

    @ApiModelProperty(value = "开始日期", required = true)
    @NotNull(message = "开始日期不能为空")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束日期", required = true)
    @NotNull(message = "结束日期不能为空")
    private LocalDateTime endDate;

    @ApiModelProperty("关联的标签ID列表")
    private Long tagId;

    @ApiModelProperty("规划名称")
    private String name;
}
