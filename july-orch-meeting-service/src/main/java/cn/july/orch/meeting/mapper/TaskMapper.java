package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务Mapper
 */
public interface TaskMapper extends BaseMapper<TaskPO> {

    /**
     * 查询超期任务
     *
     * @param currentTime 当前时间
     * @return 超期任务列表
     */
    List<TaskPO> findOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务列表
     */
    @Select("SELECT * FROM task WHERE meeting_id = #{meetingId} AND deleted = 0")
    List<TaskPO> findByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据状态查询任务列表
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<TaskPO> findByStatus(@Param("status") TaskStatusEnum status);

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务列表
     */
    List<TaskPO> findByOwner(@Param("ownerOpenId") String ownerOpenId);

    /**
     * 根据飞书任务ID查询任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 任务
     */
    TaskPO findByFeishuTaskId(@Param("feishuTaskId") String feishuTaskId);

    /**
     * 统计任务清单下的任务总数
     *
     * @param taskListId 任务清单ID
     * @return 任务总数
     */
    Long countByTaskListId(@Param("taskListId") Long taskListId);

    /**
     * 统计任务清单下已完成的任务数
     *
     * @param taskListId 任务清单ID
     * @return 已完成的任务数
     */
    Long countCompletedByTaskListId(@Param("taskListId") Long taskListId);

    /**
     * 根据会议标签筛选任务（支持分页）
     *
     * @param page 分页参数
     * @param meetingTagIds 会议标签ID列表
     * @param title 任务标题（可选）
     * @param ownerOpenId 负责人OpenID（可选）
     * @param ownerName 负责人名称（可选）
     * @param priority 优先级（可选）
     * @param status 任务状态（可选）
     * @param meetingId 会议ID（可选）
     * @param taskListId 任务清单ID（可选）
     * @param dueDateStart 截止时间开始（可选）
     * @param dueDateEnd 截止时间结束（可选）
     * @param createTimeStart 创建时间开始（可选）
     * @param createTimeEnd 创建时间结束（可选）
     * @param onlyOverdue 是否只查询超期任务（可选）
     * @return 任务分页结果
     */
    IPage<TaskPO> selectTasksByMeetingTags(
        Page<TaskPO> page,
        @Param("meetingTagIds") List<Long> meetingTagIds,
        @Param("title") String title,
        @Param("ownerOpenId") String ownerOpenId,
        @Param("ownerName") String ownerName,
        @Param("priority") Integer priority,
        @Param("status") Integer status,
        @Param("meetingId") Long meetingId,
        @Param("taskListId") Long taskListId,
        @Param("dueDateStart") LocalDateTime dueDateStart,
        @Param("dueDateEnd") LocalDateTime dueDateEnd,
        @Param("createTimeStart") LocalDateTime createTimeStart,
        @Param("createTimeEnd") LocalDateTime createTimeEnd,
        @Param("onlyOverdue") Boolean onlyOverdue
    );
}