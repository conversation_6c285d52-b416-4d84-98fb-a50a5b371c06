package cn.july.orch.meeting.common;

import cn.july.orch.meeting.properties.MeetingSeverProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @description 智能体应用常量
 * @date 2025-04-09
 */
@Component
public class AgentConstants {

    @Autowired
    private MeetingSeverProperties properties;

    /**
     * 调用智能体
     */
    public static String INVOKE_APP_URL = "/web-api/v1/chat/completions";

    /**
     * 文件汇总鉴权
     */
    public static String SUMMARY_AUTHORIZATION;

    /**
     * 问答助手鉴权
     */
    public static String QA_AUTHORIZATION;

    /**
     * 会议报告鉴权
     */
    public static String REPORT_AUTHORIZATION;

    /**
     * 会议纪要鉴权
     */
    public static String AI_TRANSCRIPT_AUTHORIZATION;

    /**
     * 会议分析智能体应用ID
     */
    public static String MEETING_ANALYSIS_APP_ID;

    /**
     * AI智能纪要智能体应用ID
     */
    public static String AI_TRANSCRIPT_APP_ID;

    /**
     * 问答助手应用ID
     */
    public static String QA_APP_ID;

    /**
     * 文件汇总应用ID
     */
    public static String FILE_SUMMARY_APP_ID;

    @PostConstruct
    public void init() {
        SUMMARY_AUTHORIZATION = properties.getAgent().getSummaryAuthorization();
        QA_AUTHORIZATION = properties.getAgent().getQaAuthorization();
        REPORT_AUTHORIZATION = properties.getAgent().getReportAuthorization();
        AI_TRANSCRIPT_AUTHORIZATION = properties.getAgent().getAiTranscriptAuthorization();
        
        MEETING_ANALYSIS_APP_ID = properties.getAgent().getMeetingAnalysisAppId();
        AI_TRANSCRIPT_APP_ID = properties.getAgent().getAiTranscriptAppId();
        QA_APP_ID = properties.getAgent().getQaAppId();
        FILE_SUMMARY_APP_ID = properties.getAgent().getFileSummaryAppId();
    }
}
