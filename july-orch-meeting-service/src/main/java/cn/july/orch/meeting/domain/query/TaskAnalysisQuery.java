package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务统计分析查询对象
 */
@Data
@ApiModel("任务统计分析查询")
public class TaskAnalysisQuery {

    @ApiModelProperty(value = "年月", required = true, example = "2025-08")
    @NotNull(message = "年月不能为空")
    private String yearMonth;

    @ApiModelProperty(value = "会议标签ID列表（支持批量筛选）")
    private List<Long> meetingTagIds;
    
    @ApiModelProperty(value = "是否使用模拟数据", example = "false")
    private Boolean useMockData = false;
}