package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.dto.MeetingStatisticsDTO;
import cn.july.orch.meeting.domain.query.MeetingStatisticsQuery;
import cn.july.orch.meeting.service.MeetingStatisticsQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Assistant
 * @description 会议统计分析控制器 - 支持月份和会议标签数组筛选
 */
@Api(tags = "会议统计分析")
@Slf4j
@RestController
@RequestMapping("/meeting-statistics")
@RequiredArgsConstructor
public class MeetingStatisticsController {

    private final MeetingStatisticsQueryService meetingStatisticsQueryService;

    @PostMapping("/analysis")
    @ApiOperation(value = "获取会议统计分析数据", notes = "支持按月份和会议标签数组筛选，输出AI功能应用、会议质量分析、典型会议等统计数据，可选useMockData参数控制是否使用模拟数据")
    public MeetingStatisticsDTO getStatistics(@Validated @RequestBody MeetingStatisticsQuery query) {
        log.info("收到会议统计分析请求，年月：{}，标签数量：{}，是否使用模拟数据：{}",
            query.getYearMonth(),
            query.getMeetingTagIds() != null ? query.getMeetingTagIds().size() : 0,
            query.getUseMockData());
        
        MeetingStatisticsDTO result = meetingStatisticsQueryService.getStatistics(query);
        
        if (Boolean.TRUE.equals(query.getUseMockData())) {
            log.info("返回模拟的会议统计数据用于图表展示");
        } else {
            log.info("会议统计分析完成，AI生成数：{}，应用占比：{}%", 
                result.getBasicStatistics().getAiGeneratedCount(),
                result.getBasicStatistics().getAiApplicationRate());
        }
        
        return result;
    }
}