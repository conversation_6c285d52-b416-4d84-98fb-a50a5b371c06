package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务统计分析结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("任务统计分析结果")
public class TaskAnalysisDTO {

    @ApiModelProperty("基础统计信息")
    private BasicStatistics basicStatistics;

    @ApiModelProperty("任务生命周期阶段分布列表")
    private List<TaskPhaseStatistics> taskPhaseStatistics;

    @ApiModelProperty("任务会议类型分布列表")
    private List<MeetingTypeDistribution> meetingTypeDistribution;

    @ApiModelProperty("不同会议标签下逾期任务排行")
    private List<TagOverdueTasksRanking> tagOverdueTasksRanking;
    
    @ApiModelProperty("任务账龄分析")
    private List<TaskAgeAnalysis> taskAgeAnalysis;

    @ApiModelProperty("优先任务排行")
    private PriorityTasks priorityTasks;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("基础统计信息")
    public static class BasicStatistics {
        @ApiModelProperty("进行中任务数")
        private Integer totalCount;

        @ApiModelProperty("任务完成率(%)")
        private Double completionRate;

        @ApiModelProperty("逾期任务总数")
        private Integer overdueTaskCount;

        @ApiModelProperty("按时完成率(%)")
        private Double onTimeCompletionRate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("任务生命周期阶段统计")
    public static class TaskPhaseStatistics {
        @ApiModelProperty("阶段名称")
        private String phaseName;

        @ApiModelProperty("任务数量")
        private Integer taskCount;

        @ApiModelProperty("占比(%)")
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("会议类型任务分布")
    public static class MeetingTypeDistribution {
        @ApiModelProperty("会议类型")
        private String meetingType;

        @ApiModelProperty("任务数量")
        private Integer taskCount;

        @ApiModelProperty("占比(%)")
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("不同会议标签下逾期任务排行")
    public static class TagOverdueTasksRanking {
        @ApiModelProperty("会议标签名称")
        private String tagName;

        @ApiModelProperty("逾期任务数")
        private Integer overdueTaskCount;

        @ApiModelProperty("总任务数")
        private Integer totalTaskCount;
        
        @ApiModelProperty("逾期率(%)")
        private Double overdueRate;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("任务账龄分析")
    public static class TaskAgeAnalysis {
        @ApiModelProperty("账龄区间")
        private String ageRange;
        
        @ApiModelProperty("任务数量")
        private Integer taskCount;
        
        @ApiModelProperty("占比(%)")
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("优先任务")
    public static class PriorityTasks {
        @ApiModelProperty("高优先级任务列表")
        private List<PriorityTaskItem> highPriorityTasks;

        @ApiModelProperty("逾期任务列表")
        private List<PriorityTaskItem> overdueTasks;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("优先任务项")
    public static class PriorityTaskItem {
        @ApiModelProperty("任务ID")
        private Long id;

        @ApiModelProperty("任务标题")
        private String title;

        @ApiModelProperty("会议标签")
        private List<SimpleMeetingTagDTO> meetingTags;

        @ApiModelProperty("优先级")
        private Integer priority;

        @ApiModelProperty("优先级名称")
        private String priorityName;

        @ApiModelProperty("负责人")
        private String ownerName;

        @ApiModelProperty("截止日期")
        private String dueDate;
    }
}