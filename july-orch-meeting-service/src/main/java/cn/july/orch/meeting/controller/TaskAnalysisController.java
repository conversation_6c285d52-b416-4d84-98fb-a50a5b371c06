package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.dto.TaskAnalysisDTO;
import cn.july.orch.meeting.domain.query.TaskAnalysisQuery;
import cn.july.orch.meeting.service.TaskAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Assistant
 * @description 任务统计分析控制器
 */
@Api(tags = "任务统计分析")
@Slf4j
@RestController
@RequestMapping("/task-analysis")
@RequiredArgsConstructor
public class TaskAnalysisController {

    private final TaskAnalysisService taskAnalysisService;

    @PostMapping("/statistics")
    @ApiOperation(value = "获取任务统计分析数据", notes = "支持按月份和会议标签数组筛选，输出任务生命周期分布、会议类型分布、完成率趋势和优先任务等统计数据，可选useMockData参数控制是否使用模拟数据")
    public TaskAnalysisDTO getStatistics(@Validated @RequestBody TaskAnalysisQuery query) {
        log.info("收到任务统计分析请求，年月：{}，标签数量：{}，是否使用模拟数据：{}",
            query.getYearMonth(),
            query.getMeetingTagIds() != null ? query.getMeetingTagIds().size() : 0,
            query.getUseMockData());
        
        TaskAnalysisDTO result = taskAnalysisService.getStatistics(query);
        
        if (Boolean.TRUE.equals(query.getUseMockData())) {
            log.info("返回模拟的任务统计数据用于图表展示");
        } else {
            log.info("任务统计分析完成，任务总数：{}，完成率：{}%", 
                result.getBasicStatistics().getTotalCount(),
                result.getBasicStatistics().getCompletionRate());
        }
        
        return result;
    }
}