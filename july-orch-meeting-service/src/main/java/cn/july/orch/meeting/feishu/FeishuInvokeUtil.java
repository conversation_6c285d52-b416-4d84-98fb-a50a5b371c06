package cn.july.orch.meeting.feishu;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.exception.FeishuException;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FeishuInvokeUtil {

    /**
     * 通用API调用入口
     */
    public static <P, R> R invoke(FeishuBaseRequest baseRequest, P param, FeishuApiFunction<Client, P, R> apiFunc) {
        long start = System.currentTimeMillis();
        log.info("[Feishu] API调用开始, appId={}, param={}", baseRequest.getAppId(), JsonUtils.toJson(param));
        Client client = getClient(baseRequest);
        try {
            BaseResponse<R> response = apiFunc.apply(client, param);
            long cost = System.currentTimeMillis() - start;
            if (response == null) {
                log.error("[Feishu] API返回为空, appId={}, cost={}ms", baseRequest.getAppId(), cost);
                throw new FeishuException("API返回为空");
            }
            if (response.getCode() != 0) {
                log.error("[Feishu] API调用失败, code={}, msg={}, appId={}, resp={}, cost={}ms", response.getCode(), response.getMsg(), baseRequest.getAppId(), JsonUtils.toJson(response), cost);
                throw new FeishuException("API调用失败: " + response.getMsg());
            }
            log.info("[Feishu] API调用成功, code=0, appId={}, resp={}, cost={}ms", baseRequest.getAppId(), JsonUtils.toJson(response), cost);
            return response.getData();
        } catch (Exception e) {
            log.error("[Feishu] API调用异常, appId={}, param={}, error={}", baseRequest.getAppId(), param, e.getMessage(), e);
            throw new FeishuException("API调用异常: " + e.getMessage());
        }
    }

    /**
     * 获取 Feishu Client
     */
    private static Client getClient(FeishuBaseRequest baseRequest) {
        Client client = FeishuClientManager.getClient(baseRequest.getAppId(), baseRequest.getAppSecret());
        if (client == null) {
            log.error("[Feishu] 获取Client失败, appId={}, appSecret={}", baseRequest.getAppId(), baseRequest.getAppSecret());
            throw new FeishuException("获取Client失败");
        }
        return client;
    }

}
