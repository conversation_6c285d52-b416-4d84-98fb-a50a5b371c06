package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.model.page.PageResultDTO;
import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.command.NewMeetingQuery;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import cn.hutool.core.date.DateUtil;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 新会议查询服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingQueryService {

    private final NewMeetingDomainService newMeetingDomainService;
    private final NewMeetingAssembler newMeetingAssembler;
    private final UserInfoAssembler userInfoAssembler;
    private final FeishuAppClient feishuAppClient;
    private final NewMeetingMapper newMeetingMapper;
    private final FileDetailService fileDetailService;
    private final FileStorageService fileStorageService;

    /**
     * 根据ID查询会议详情
     */
    public NewMeetingDTO getById(Long id) {
        NewMeeting meeting = newMeetingDomainService.findById(id);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        return dto;
    }

    /**
     * 根据飞书日程事件ID查询会议
     */
    public NewMeetingDTO findByFsCalendarEventId(String fsCalendarEventId) {
        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        return dto;
    }

    /**
     * 根据飞书会议ID查询会议
     */
    public NewMeetingDTO findByFsMeetingId(String fsMeetingId) {
        NewMeeting meeting = newMeetingDomainService.findByFsMeetingId(fsMeetingId);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        return dto;
    }

    /**
     * 分页查询新会议列表
     */
    public PageResultDTO<NewMeetingListDTO> queryPage(NewMeetingQuery query) {
        Page<NewMeetingPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<NewMeetingListDTO> resultPage = newMeetingMapper.queryPage(page, query);

        // 填充用户详细信息
        List<NewMeetingListDTO> records = resultPage.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            fillUserInfoForList(records);
        }

        return new PageResultDTO<>(query.getPageNum(), query.getPageSize(), resultPage.getTotal(), records);
    }

    /**
     * 查询所有会议（不分页）
     */
    public List<NewMeetingDTO> listAll(NewMeetingQuery query) {
        List<NewMeeting> meetings = newMeetingDomainService.findPage(
                query.getMeetingName(),
                query.getStatus() != null ? query.getStatus().getCode() : null,
                query.getPriorityLevel() != null ? query.getPriorityLevel().getCode() : null,
                query.getMeetingPlanId(),
                query.getMeetingStandardId(),
                query.getStartTimeFrom() != null ? query.getStartTimeFrom().toString() : null,
                query.getStartTimeTo() != null ? query.getStartTimeTo().toString() : null,
                query.getCreateUserId(),
                1,
                Integer.MAX_VALUE
        );

        List<NewMeetingDTO> dtoList = newMeetingAssembler.toDTOList(meetings);
        dtoList.forEach(this::fillUserInfo);
        return dtoList;
    }

    /**
     * 填充用户信息
     */
    private void fillUserInfo(NewMeetingDTO dto) {
        // 收集所有需要查询的用户ID，并进行去重
        List<String> userIds = dto.getAttendees() != null ? new ArrayList<>(dto.getAttendees()) : new ArrayList<>();

        // 添加主持人ID（如果不在参会人员列表中）
        if (dto.getHostUserId() != null && !userIds.contains(dto.getHostUserId())) {
            userIds.add(dto.getHostUserId());
        }

        // 添加记录员ID（如果不在参会人员列表中）
        if (dto.getRecorderUserId() != null && !userIds.contains(dto.getRecorderUserId())) {
            userIds.add(dto.getRecorderUserId());
        }

        // 添加实际参会人员ID（如果不在列表中）
        if (!CollectionUtils.isEmpty(dto.getActualAttendees())) {
            for (String actualAttendeeId : dto.getActualAttendees()) {
                if (!userIds.contains(actualAttendeeId)) {
                    userIds.add(actualAttendeeId);
                }
            }
        }

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        try {
            // 调用飞书API获取用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(userIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, user -> user));

            // 填充参会人员详细信息
            if (!CollectionUtils.isEmpty(dto.getAttendees())) {
                List<FSUserInfoDTO> attendeeDetails = dto.getAttendees().stream()
                        .map(userId -> userMap.get(userId))
                        .filter(user -> user != null)
                        .map(userInfoAssembler::user2DTO)
                        .collect(Collectors.toList());
                dto.setAttendeeDetails(attendeeDetails);
            }

            // 填充实际参会人员详细信息
            if (!CollectionUtils.isEmpty(dto.getActualAttendees())) {
                List<FSUserInfoDTO> actualAttendeeDetails = dto.getActualAttendees().stream()
                        .map(userId -> userMap.get(userId))
                        .filter(user -> user != null)
                        .map(userInfoAssembler::user2DTO)
                        .collect(Collectors.toList());
                dto.setActualAttendeeDetails(actualAttendeeDetails);
            }

            // 填充主持人详细信息
            if (dto.getHostUserId() != null) {
                User hostUser = userMap.get(dto.getHostUserId());
                if (hostUser != null) {
                    dto.setHostUserDetail(userInfoAssembler.user2DTO(hostUser));
                }
            }

            // 填充记录员详细信息
            if (dto.getRecorderUserId() != null) {
                User recorderUser = userMap.get(dto.getRecorderUserId());
                if (recorderUser != null) {
                    dto.setRecorderUserDetail(userInfoAssembler.user2DTO(recorderUser));
                }
            }
        } catch (Exception e) {
            // 如果飞书API调用失败，不影响主要功能，只记录日志
            // 这里可以根据需要决定是否抛出异常
        }
    }

    /**
     * 为列表填充用户信息
     */
    private void fillUserInfoForList(List<NewMeetingListDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        try {
            // 收集所有需要查询的用户ID，参考 TeamQueryService.arrangeMemberInfo 的实现
            List<String> openIds = dtoList.stream()
                    .flatMap(dto -> Stream.concat(
                            Stream.concat(
                                    Stream.concat(
                                            Optional.ofNullable(dto.getAttendees()).orElse(Collections.emptyList()).stream(),
                                            Optional.ofNullable(dto.getHostUserId()).map(Stream::of).orElseGet(Stream::empty)
                                    ),
                                    Optional.ofNullable(dto.getRecorderUserId()).map(Stream::of).orElseGet(Stream::empty)
                            ),
                            Optional.ofNullable(dto.getActualAttendees()).orElse(Collections.emptyList()).stream()
                    )).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(openIds)) {
                return;
            }

            // 批量获取用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(openIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, Function.identity()));

            // 为每个DTO填充用户信息
            dtoList.forEach(item -> {
                // 填充参会人员详细信息
                if (CollUtil.isNotEmpty(item.getAttendees())) {
                    List<User> attendees = item.getAttendees().stream()
                            .map(userMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(attendees)) {
                        item.setAttendeeDetails(userInfoAssembler.user2DTO(attendees));
                    }
                }

                // 填充实际参会人员详细信息
                if (CollUtil.isNotEmpty(item.getActualAttendees())) {
                    List<User> actualAttendees = item.getActualAttendees().stream()
                            .map(userMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(actualAttendees)) {
                        item.setActualAttendeeDetails(userInfoAssembler.user2DTO(actualAttendees));
                    }
                }

                // 填充主持人详细信息
                if (StrUtil.isNotBlank(item.getHostUserId())) {
                    User hostUser = userMap.get(item.getHostUserId());
                    if (ObjUtil.isNotNull(hostUser)) {
                        item.setHostUserDetail(userInfoAssembler.user2DTO(hostUser));
                    }
                }

                // 填充记录员详细信息
                if (StrUtil.isNotBlank(item.getRecorderUserId())) {
                    User recorderUser = userMap.get(item.getRecorderUserId());
                    if (ObjUtil.isNotNull(recorderUser)) {
                        item.setRecorderUserDetail(userInfoAssembler.user2DTO(recorderUser));
                    }
                }
            });
        } catch (Exception e) {
            log.warn("填充用户信息失败", e);
        }
    }

    /**
     * 为会前文档补充临时访问链接
     */
    public void fillPreMeetingFileUrls(NewMeetingDTO dto) {
        if (dto == null || CollUtil.isEmpty(dto.getPreMeetingDocuments())) {
            return;
        }

        try {
            for (PreMeetingDocumentDTO doc : dto.getPreMeetingDocuments()) {
                if (doc == null || StrUtil.isBlank(doc.getFileKey())) {
                    continue;
                }
                FileDetailPO fileDetailPO = fileDetailService.getById(doc.getFileKey());
                if (fileDetailPO == null || StrUtil.isBlank(fileDetailPO.getUrl())) {
                    continue;
                }
                FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                String presignedUrl = fileStorageService.generatePresignedUrl(
                        fileInfo,
                        DateUtil.offsetMinute(new Date(), 60)
                );
                doc.setUrl(presignedUrl);
            }
        } catch (Exception e) {
            log.warn("填充会前文档临时链接失败", e);
        }
    }
}
