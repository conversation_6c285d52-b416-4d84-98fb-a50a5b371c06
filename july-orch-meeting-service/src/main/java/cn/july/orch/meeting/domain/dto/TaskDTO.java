package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务DTO
 */
@Data
@ApiModel("任务信息")
public class TaskDTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("关联的任务清单ID")
    private Long taskListId;
    
    @ApiModelProperty("任务清单名称")
    private String taskListName;
    
    @ApiModelProperty("任务清单描述")
    private String taskListDescription;

    @ApiModelProperty("父任务ID")
    private Long parentId;

    @ApiModelProperty("飞书任务ID")
    private String feishuTaskId;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("负责人OpenID")
    private String ownerOpenId;

    @ApiModelProperty("负责人名称")
    private String ownerName;

    @ApiModelProperty("负责人头像")
    private String ownerAvatarUrl;

    @ApiModelProperty("优先级")
    private TaskPriorityEnum priority;

    @ApiModelProperty("任务状态")
    private TaskStatusEnum status;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty("实际完成时间")
    private LocalDateTime completedAt;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    // ==================== 会议信息 ====================
    
    @ApiModelProperty("关联的会议基本信息")
    private MeetingBasicInfoDTO meetingInfo;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建用户名")
    private String createUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新用户ID")
    private String updateUserId;

    @ApiModelProperty("更新用户名")
    private String updateUserName;

    @ApiModelProperty("附件信息列表")
    private List<FileInfoDTO> attachments;

    // ==================== 子任务信息 ====================
    
    @ApiModelProperty("子任务列表")
    private List<TaskListDTO> subTasks;
    
    @ApiModelProperty("子任务完成情况统计（如“2/5”）")
    private String subTaskSummary;
    
    @ApiModelProperty("子任务总数")
    private Integer subTaskTotal;
    
    @ApiModelProperty("子任务完成数")
    private Integer subTaskCompleted;

    // ==================== 任务动态信息 ====================
    
    @ApiModelProperty("任务动态列表")
    private List<TaskActivityDTO> activities;
    
    @ApiModelProperty("动态数量")
    private Integer activityCount;
    
    @ApiModelProperty("最新动态")
    private TaskActivityDTO latestActivity;
}
