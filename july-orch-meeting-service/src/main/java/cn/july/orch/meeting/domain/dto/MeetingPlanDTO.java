package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划详情DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议规划名称
     */
    private String planName;

    /**
     * 会议规划描述/备注
     */
    private String planDescription;

    /**
     * 会议标准ID
     */
    private Long meetingStandardId;

    private MeetingStandardDTO meetingStandard;

    /**
     * 计划持续时长(分钟)
     */
    private Integer plannedDuration;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 重复会议cron
     */
    private String cron;

    private String uuid;

    /**
     * 重复结束日期
     */
    private LocalDate recurrenceEndDate;

    /**
     * 会议规划状态
     */
    private MeetingPlanStatusEnum status;

    /**
     * 关联标签id
     */
    private List<Long> tagIds;

    private List<MeetingTagDTO> tags;

    /**
     * 提醒人员列表
     */
    private List<String> attendees;

    private List<FSUserInfoDTO> attendeeDetails;

    /**
     * 提前通知发送标记(0-未发送,1-已发送)
     */
    private Integer advanceNoticeSent;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 会前文档文件ID列表
     */
    private List<String> preMeetingDocuments;

    /**
     * 关联会议信息
     */
    private List<NewMeetingDTO> newMeetings;

}
