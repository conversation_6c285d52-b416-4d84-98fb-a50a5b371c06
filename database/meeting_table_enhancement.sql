-- 会议表结构改造脚本
-- 执行时间：2025-08-29

-- 1. 为 new_meeting 表添加"是否启用文档AI汇总"开关字段
ALTER TABLE new_meeting
ADD COLUMN enable_doc_ai_summary tinyint(1) DEFAULT 0 NOT NULL 
COMMENT '是否启用会前文档AI汇总 (0-不启用, 1-启用)' 
AFTER instance_sequence;

-- 2. 修改 pre_meeting_documents 字段的注释，明确其新的JSON结构
ALTER TABLE new_meeting
MODIFY COLUMN pre_meeting_documents json NULL 
COMMENT '会前文档列表, 结构为对象数组: [{"file_key": "...", "file_name": "...", "ai_summary_status": "PENDING/SUCCESS/FAILED", "ai_summary_content": "..."}]';

-- 3. 添加会议标签字段（类似任务表的标签字段）
ALTER TABLE new_meeting
ADD COLUMN meeting_tags_json json NULL 
COMMENT '会议标签列表（TaskMeetingTagDTO对象数组，包含id、name、color）' 
AFTER enable_doc_ai_summary;

-- 注意：执行前请备份数据库，确保在非生产环境先测试