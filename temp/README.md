# 临时文件目录

这个目录用于存储会议附件AI汇总处理过程中产生的临时文件。

## 目录说明

- **用途**: 存储AI汇总生成的Markdown文件，用于上传到飞书云盘
- **生命周期**: 临时文件在处理完成后会立即删除
- **清理机制**: 
  - 处理完成后立即删除
  - 定时任务每小时清理超过1小时的过期文件
  - 系统重启时不会保留任何临时文件

## 文件命名规则

临时文件使用以下命名规则：
```
{UUID}_{会议标题}_会前文档汇总.md
```

例如：
```
a1b2c3d4-e5f6-7890-abcd-ef1234567890_项目评审会议_会前文档汇总.md
```

## 注意事项

1. **不要手动修改**: 此目录中的文件由系统自动管理，请勿手动修改或删除
2. **磁盘空间**: 临时文件通常很小（几KB到几MB），不会占用大量磁盘空间
3. **权限**: 确保应用有读写此目录的权限
4. **备份**: 此目录不需要备份，所有文件都是临时的

## 故障排除

如果遇到临时文件相关的问题：

1. **权限问题**: 检查应用是否有读写此目录的权限
2. **磁盘空间**: 检查磁盘空间是否充足
3. **文件锁定**: 如果文件被锁定无法删除，重启应用后会自动清理

## 配置

临时文件管理通过以下配置控制：

- **清理间隔**: 每小时清理一次（3600000毫秒）
- **文件过期时间**: 1小时
- **目录位置**: 项目根目录下的 `temp` 文件夹

如需修改配置，请编辑 `TempFileCleanupConfig.java` 文件。